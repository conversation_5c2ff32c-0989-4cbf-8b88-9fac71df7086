import { useState, useEffect } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";

let systemMasterDataCache = null;

export const useSystemMasterData = () => {
  const [systems, setSystems] = useState([]);

  useEffect(() => {
    const fetchSystems = async () => {
      if (systemMasterDataCache) {
        setSystems(systemMasterDataCache);
        return;
      }
      try {
        const res = await getMasterData({ groups: ["system"] });
        if (res.data && Array.isArray(res.data.system)) {
          systemMasterDataCache = res.data.system;
          setSystems(res.data.system);
        } else {
          throw new Error("Invalid response");
        }
      } catch (error) {
        toast.error("Error fetching system master data");
      }
    };
    fetchSystems();
  }, []);

  return { systems };
};
