import React, { useState } from "react";
import GenericTable from "../GenericTable";
import Button from "./Button";

const Alert = ({ isOpen, data, screeningId, onClose, onSubmit }) => {
  const [reason, setReason] = useState("");
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => setShow(true), 10);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const columns = [
    { name: "First Name", selector: (row) => row.first_name, sortable: true },
    { name: "Last Name", selector: (row) => row.last_name, sortable: true },
    { name: "DOB", selector: (row) => row.dob, sortable: true },
    { name: "Gender", selector: (row) => row.gender, sortable: true },
    { name: "Hair Color", selector: (row) => row.hair_color, sortable: true },
    { name: "Added By", selector: (row) => row.added_by, sortable: true },
    { name: "Match Type", selector: (row) => row.match_type, sortable: true },
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!reason.trim()) {
      console.error("❌ Reason is required.");
      return;
    }
    if (typeof onSubmit === "function") {
      onSubmit(screeningId, reason); // Call the parent-provided function
    } else {
      console.error("❌ onSubmit is not a function.");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Screening Alert</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
            <div className="p-6">

              <GenericTable
                title="Screening Details"
                columns={columns}
                data={data}
                fixedHeader
                fixedHeaderScrollHeight="300px"
                highlightOnHover
                striped
                showAddButton={false}
              />

              <form onSubmit={handleSubmit} className="mt-4">
                <div className="flex items-center mb-4">
                  <label htmlFor="reason" className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Reason *
                  </label>
                  <div className="w-3/4">
                    <textarea
                      id="reason"
                      name="reason"
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-1"
                      rows="4"
                      placeholder="Enter your reason here…"
                    />
                  </div>
                </div>

                <div className="flex justify-center gap-4 mt-6">
                  <Button type="cancel" label="Cancel" onClick={() => {
                    setShow(false);
                    setTimeout(onClose, 700);
                  }} />
                  <Button type="primary" label="Override" />
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Alert;
