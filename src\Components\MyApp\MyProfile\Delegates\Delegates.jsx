import React, { useState, useMemo, useEffect } from "react";
import GenericTable from "../../../GenericTable";
import AddDelegateModal from "./AddDelegateModal";

// Custom debounce hook
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

const Delegates = ({ delegatesData: apiDelegatesData, loading }) => {
  const [myDelegatesSearchTerm, setMyDelegatesSearchTerm] = useState("");
  const [delegatesToMeSearchTerm, setDelegatesToMeSearchTerm] = useState("");

  // Debounce the search terms with 300ms delay
  const debouncedMyDelegatesSearch = useDebounce(myDelegatesSearchTerm, 300);
  const debouncedDelegatesToMeSearch = useDebounce(delegatesToMeSearchTerm, 300);

  // Default delegates data if API data is not available
  const defaultMyDelegatesData = [
    {
      name: "DAVID STOLLER",
      eid: "422353",
      taskType: "Access Area Audit",
      sendNotificationTo: "Delegate & Owner",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
    },
    {
      name: "STEVEN KRUSCHE",
      eid: "3545",
      taskType: "Meeting Site",
      sendNotificationTo: "Delegate & Owner",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
    },
  ];

  const defaultDelegatesToMeData = [
    {
      name: "DAVID STOLLER",
      eid: "422353",
      taskType: "Access Area Audit",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
      status: "Expired",
    },
    {
      name: "STEVEN KRUSCHE",
      eid: "3545",
      taskType: "Meeting Site",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
      status: "Expired",
    },
  ];

  const [myDelegatesData, setMyDelegatesData] = useState(
    apiDelegatesData?.myDelegates || defaultMyDelegatesData
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  const delegatesToMeData = apiDelegatesData?.delegatesToMe || defaultDelegatesToMeData;

  // Update delegates data when API data changes
  React.useEffect(() => {
    if (apiDelegatesData?.myDelegates) {
      setMyDelegatesData(apiDelegatesData.myDelegates);
    }
  }, [apiDelegatesData]);

  const myDelegatesColumns = [
    { name: "Name", selector: (row) => row.name, sortable: true },
    { name: "EID", selector: (row) => row.eid },
    { name: "Task Type", selector: (row) => row.taskType },
    { name: "Send Notification To", selector: (row) => row.sendNotificationTo },
    { name: "Start Date", selector: (row) => row.startDate },
    { name: "End Date", selector: (row) => row.endDate },
  ];

  const delegatesToMeColumns = [
    { name: "Name", selector: (row) => row.name, sortable: true },
    { name: "EID", selector: (row) => row.eid },
    { name: "Task Type", selector: (row) => row.taskType },
    { name: "Start Date", selector: (row) => row.startDate },
    { name: "End Date", selector: (row) => row.endDate },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <div
          className={
            row.status === "Valid"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683] px-2 py-1 rounded"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F] px-2 py-1 rounded"
          }
        >
          {row.status}
        </div>
      ),
    },
  ];

  const filteredMyDelegatesData = useMemo(() => {
    if (!debouncedMyDelegatesSearch) return myDelegatesData;
    return myDelegatesData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(debouncedMyDelegatesSearch.toLowerCase())
      )
    );
  }, [myDelegatesData, debouncedMyDelegatesSearch]);

  const filteredDelegatesToMeData = useMemo(() => {
    if (!debouncedDelegatesToMeSearch) return delegatesToMeData;
    return delegatesToMeData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(debouncedDelegatesToMeSearch.toLowerCase())
      )
    );
  }, [delegatesToMeData, debouncedDelegatesToMeSearch]);

  const handleAddDelegate = (newDelegate) => {
    const {
      searchIdentity: { name, eid },
      taskType,
      sendNotificationTo,
      startDate: rawStart,
      endDate: rawEnd,
    } = newDelegate;

    // helper to convert Date objects to readable strings
    const formatDate = (d: string | Date) =>
      d instanceof Date ? d.toLocaleString() : d;

    const startDate = formatDate(rawStart);
    const endDate = formatDate(rawEnd);

    setMyDelegatesData([
      { name, eid, taskType, sendNotificationTo, startDate, endDate },
      ...myDelegatesData,
    ]);
  };

  if (loading) {
    return (
      <div>
        <div className="mb-4 bg-white rounded-[10px] p-4">
          <h3 className="font-poppins text-[14px] text-[#4F2683] font-[500] mb-4">My Delegates</h3>
          <div className="flex justify-center items-center py-8">
            <p className="text-[#7C7C7C] font-poppins text-[12px]">Loading delegates...</p>
          </div>
        </div>
        <div className="bg-white rounded-[10px] p-4">
          <h3 className="font-poppins text-[14px] text-[#4F2683] font-[500] mb-4">Delegates to Me</h3>
          <div className="flex justify-center items-center py-8">
            <p className="text-[#7C7C7C] font-poppins text-[12px]">Loading delegates...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-4 bg-white rounded-[10px]">
        <GenericTable
          title="My Delegates"
          showAddButton={true}
          onAdd={() => setIsModalOpen(true)}
          searchTerm={myDelegatesSearchTerm}
          onSearchChange={(e) => setMyDelegatesSearchTerm(e.target.value)}
          columns={myDelegatesColumns}
          data={filteredMyDelegatesData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>

      <AddDelegateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onAdd={handleAddDelegate}
      />

      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Delegates to Me"
          showAddButton={false}
          searchTerm={delegatesToMeSearchTerm}
          onSearchChange={(e) => setDelegatesToMeSearchTerm(e.target.value)}
          columns={delegatesToMeColumns}
          data={filteredDelegatesToMeData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>
    </div>
  );
};

export default Delegates;
