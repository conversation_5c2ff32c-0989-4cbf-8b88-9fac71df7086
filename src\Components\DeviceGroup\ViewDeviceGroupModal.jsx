import React, { useState, useEffect } from "react";

const ViewDeviceGroupModal = ({ isOpen, row, onClose }) => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => setShow(true), 10);
    } else {
      setShow(false);
    }
  }, [isOpen]);

  if (!isOpen || !row) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Device Group Details
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <div className="bg-white p-6 rounded-lg flex flex-col gap-4">
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal">Device Group</label>
            <div className="w-3/4">
              <input
                type="text"
                value={row.deviceGroup}
                disabled
                className="w-full border-none bg-transparent rounded p-2 text-[#8F8F8F]"
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal">Assign App</label>
            <div className="w-3/4">
              <input
                type="text"
                value={row.assignApp}
                disabled
                className="w-full border-none bg-transparent rounded p-2 text-[#8F8F8F]"
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal">Status</label>
            <div className="w-3/4">
              <input
                type="text"
                value={row.status}
                disabled
                className="w-full border-none bg-transparent rounded p-2 text-[#8F8F8F]"
              />
            </div>
          </div>
          <div className="flex justify-end">
            <button
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
              onClick={handleClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewDeviceGroupModal;
