import React, { useState, useEffect } from "react";
import GenericTable from "../GenericTable";
import Swal from "sweetalert2";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import DeniedGuestsModal from "./DeniedGuestsModal";
import AddDeniedGuestForm from "./AddDeniedGuestForm";
import TruncatedRow from "../Tooltip/TrucantedRow";
import Delete from "../../Images/Delete.svg";
import {
  getDeniedGuests,
  deletePatientGuest,
  getPatientHistory,
  addPatientGuest,updatePatientGuestDetails,
} from "../../api/PatientHub"; // Import the API functions
import PatientHistoryModal from "./PatientHistoryModal"; // Import the PatientHistoryModal
import { PiClockCountdown } from "react-icons/pi";
// import { id } from "date-fns/locale";
import formatDateTime from "../../utils/formatDateTime";

const DeniedGuests = ({ patientId }) => {
  // State variables
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRow, setSelectedRow] = useState(null);
  const [isViewModalOpen, setViewModalOpen] = useState(false);
  const [isAddModalOpen, setAddModalOpen] = useState(false);
  
  const [newEntry, setNewEntry] = useState({
     firstName: "",
  lastName: "",
    denialReason: "",
    Email: "",
    phone: "",
    dateDenied: "",
  });
  const [hoveredRow, setHoveredRow] = useState(null);
  const [sortBy, setSortBy] = useState("first_name"); // Default sort column
  const [sortOrder, setSortOrder] = useState("ASC"); // Default sort order

  const [isHistoryModalOpen, setHistoryModalOpen] = useState(false);
 const [isHistoryLoading, setHistoryLoading] = useState(false);
  const [historyData, setHistoryData] = useState([]); // State for history data

  // Function to fetch denied guests
  const fetchDeniedGuests = async () => {
    try {
      const response = await getDeniedGuests({
          patient_id: patientId,
        guest_type: 2, // Include guest_type: 2
        sortBy, // Pass sortBy to the API
        sortOrder, // Pass sortOrder to the API
        search: searchTerm, // Pass searchTerm to the API
      });
      const formattedData = response.map((guest) => ({
        first_name: guest.first_name  || "",   // ← add this
last_name:  guest.last_name   || "",   // ← and this
        id: guest.patient_guest_id,
        guestName: `${guest.first_name || ""} ${guest.last_name || ""}`.trim(),
        denialReason: guest.reason, // Handle null reasons
        contactEmail: guest.email, // Handle null emails
        contactNumber: guest.phone, // Handle null phone numbers
        dateDenied: guest.birth_date
    ? formatDateTime(guest.birth_date)
     : "",
      }));
      setData(formattedData);
      setFilteredData(formattedData);
    } catch (error) {
      console.error("Error fetching denied guests:", error);
      toast.error("Failed to fetch denied guests.", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
    }
  };

  // Fetch denied guests on component mount and when dependencies change
  useEffect(() => {
    if (patientId) fetchDeniedGuests(); // Fetch data when patientId is available
  }, [patientId, sortBy, sortOrder, searchTerm]); // Add searchTerm as a dependency

  // Handle search input changes
  const handleSearch = (e) => {
    setSearchTerm(e.target.value); // Update searchTerm state
  };

  // Handle table sorting
  const handleSort = (column, direction) => {
    setSortBy(column.id); // Update sortBy state
    setSortOrder(direction.toUpperCase()); // Update sortOrder state
  };

// Update a guest record (from the view/edit modal)
const handleUpdate = async (updatedGuest) => {
  try {
    const updated = await updatePatientGuestDetails(updatedGuest.id, {
      first_name: updatedGuest.first_name,
      last_name:  updatedGuest.last_name,
      reason:     updatedGuest.denialReason,
      email:      updatedGuest.contactEmail,
      phone:      updatedGuest.contactNumber,
      birth_date:  updatedGuest.dateDenied,
    });

    // normalize API response to your table row shape
    const normalized = {
  id: updated.patient_guest_id,
  guestName: `${updated.first_name || ""} ${updated.last_name || ""}`.trim(),
  first_name: updated.first_name || "",
  last_name: updated.last_name || "",
  denialReason: updated.reason,
  contactEmail: updated.email,
  contactNumber: updated.phone,
 dateDenied: updated.birth_date
       ? formatDateTime(updated.birth_date)
       : updatedGuest.dateDenied
};

    const updatedData = data.map(d =>
      d.id === normalized.id ? normalized : d
    );

    setData(updatedData);
    setFilteredData(updatedData); // ← pass the same array here
    setViewModalOpen(false);
    toast.success("Denied guest updated.");
  } catch (err) {
    toast.error("Update failed, please try again.");
    setViewModalOpen(false);
  }
};

  // Delete a guest record
  const handleDelete = async (id) => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this record?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          await deletePatientGuest(id); // Call the API to delete the guest
          const updatedData = data.filter((item) => item.id !== id);
          setData(updatedData);
          setFilteredData(updatedData);
          toast.success("Record deleted successfully!", {
            position: "top-right",
            autoClose: 3000,
            hideProgressBar: true,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
          });
        } catch (error) {
          console.error("Error deleting guest:", error);
          toast.error("Failed to delete the record. Please try again.", {
            position: "top-right",
            autoClose: 3000,
            hideProgressBar: true,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
          });
        }
      }
    });
  };

  // Save a new guest (from the Add form)
  const handleSave = async () => {
    try {
      const payload = {
      first_name: newEntry.firstName || "",
  last_name: newEntry.lastName || "",
        email: newEntry.contactEmail,
        phone: newEntry.phone,
        guest_type: 2, // Set guest_type to 2 for denied guests
        reason: newEntry.denialReason, // Use denial reason
        birth_date: new Date().toISOString(), // Current date and time
        patient_id: patientId,
        relationship_type: 0, // Default value for relationship_type
      };
      await addPatientGuest(payload, 0);
      
      // Refresh the denied guests list
      await fetchDeniedGuests();
      
      setAddModalOpen(false);
      toast.success("Denied guest added successfully!", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
    } catch (error) {
      console.error("Error adding denied guest:", error);
      toast.error(
        error?.response?.data?.message ||
          "Failed to add the denied guest. Please try again.",
        {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: true,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
        }
      );
    }
  };

  // Handle history modal click
  const handleHistoryClick = async () => {
    setHistoryLoading(true);
    try {
      // Fetch patient history data using the API and pass the patientId
      const response = await getPatientHistory({ patient_id: patientId });
      console.log("Patient History Response:", response); // Log the response for debugging
      setHistoryData(response.data); // Set the fetched data to the state
      setHistoryModalOpen(true); // Open the history modal
    } catch (error) {
      console.error("Error fetching patient history:", error);
      toast.error("Failed to fetch patient history.", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: "light",
      });}finally {
      setHistoryLoading(false); // Set loading state to false after fetching
    }
  };

  // ---------------- Table Columns ----------------
  const columns = [
    {
  id: "first_name",
  name: "Name",
  selector: (row) => row.guestName,
  sortable: true,
  cell: (row) => (
    <div
      className="cursor-pointer hover:underline"
      onClick={() => {
        setSelectedRow(row);
        setViewModalOpen(true);
      }}
    >
      {row.guestName}
    </div>
  ),
},
{
      name: "Date Of Birth",
      selector: (row) => row.dateDenied,
      cell: (row) => <TruncatedRow text={row.dateDenied} />,
    },
   
   {
      id: "email",
      name: "Email",
      selector: (row) => row.contactEmail,
      cell: (row) => <TruncatedRow text={row.contactEmail} />,
      sortable: true,
    },
    {
      id: "phone",
      name: "Phone",
      selector: (row) => row.contactNumber,
      cell: (row) => <TruncatedRow text={row.contactNumber} />,
      sortable: true,
    },
     
    
     {
      id: "reason",
      name: "Reason",
      selector: (row) => row.denialReason,
      cell: (row) => <TruncatedRow text={row.denialReason} />,
      sortable: true,
    },
    
    {
      name: "Action",
      cell: (row) => (
        <div className="flex items-center gap-2">
          <img
            src={Delete}
            alt="Delete"
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer"
            onClick={() => handleDelete(row.id)}
          />
            
             <PiClockCountdown
               className="font-[500] text-xl p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer"
               onClick={handleHistoryClick}
             />
           
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
     <div className="relative">
    {/* Full-page spinner overlay */}
   {isHistoryLoading && (
  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
    <div className="relative w-16 h-16">
      {/* Pulsing outer ring */}
      <div className="absolute inset-0 rounded-full bg-[#4F2683] opacity-75 animate-ping" />
      {/* Static inner ring */}
      <div className="relative rounded-full h-full w-full border-4 border-[#4F2683]" />
    </div>
  </div>
)}

      {/* Generic Table */}
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Denied Guests"
          searchTerm={searchTerm}
          onSearchChange={handleSearch} // Pass search handler
          onAdd={() => setAddModalOpen(true)}
          columns={columns}
          data={filteredData}
          onSort={handleSort} // Pass sorting handler
          sortServer // Enable server-side sorting
          showSearch={true}
          showAddButton={true}
        />
      </div>
      <ToastContainer />

      {/* View/Edit Modal */}
      {isViewModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <DeniedGuestsModal
              guestData={selectedRow}
              onUpdate={handleUpdate}
              onClose={() => setViewModalOpen(false)}
            />
          </div>
        </div>
      )}

      {/* Add Modal */}
      {isAddModalOpen && (
        <AddDeniedGuestForm
          newEntry={newEntry}
          setNewEntry={setNewEntry}
          onSave={handleSave}
          onClose={() => setAddModalOpen(false)}
        />
      )}

      {/* History Modal */}
      {isHistoryModalOpen && (
        <PatientHistoryModal
          onClose={() => setHistoryModalOpen(false)}
          historyData={historyData}
        />
      )}
    </div>
  );
};

export default DeniedGuests;