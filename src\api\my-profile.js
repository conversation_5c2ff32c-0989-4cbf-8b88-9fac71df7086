import api from "./";

/**
 * Get current user profile details.
 *
 * @returns {Promise<any>} A promise that resolves to the user profile data.
 */
export const getProfile = async () => {
  const response = await api.get("/my-profile");
  return response.data;
};

/**
 * Get current user's cards.
 *
 * @returns {Promise<any>} A promise that resolves to the user's cards data.
 */
export const getProfileCards = async () => {
  const response = await api.get("/my-profile/cards");
  return response.data;
};

/**
 * Get current user's access records.
 *
 * @returns {Promise<any>} A promise that resolves to the user's access records.
 */
export const getProfileAccess = async () => {
  const response = await api.get("/my-profile/access");
  return response.data;
};

/**
 * Get current user's vehicles.
 *
 * @returns {Promise<any>} A promise that resolves to the user's vehicles data.
 */
export const getProfileVehicles = async () => {
  const response = await api.get("/my-profile/vehicles");
  return response.data;
};

/**
 * Add a new vehicle for the current user.
 *
 * @param {object} vehicleData - The vehicle data to add.
 * @returns {Promise<any>} A promise that resolves to the created vehicle data.
 */
export const addProfileVehicle = async (vehicleData) => {
  const response = await api.post("/my-profile/vehicle", vehicleData);
  return response.data;
};

/**
 * Get current user's delegation information.
 *
 * @returns {Promise<any>} A promise that resolves to the user's delegation data.
 */
export const getProfileDelegates = async () => {
  const response = await api.get("/my-profile/delegates");
  return response.data;
};

/**
 * Create a new delegation for another user.
 *
 * @param {object} delegationData - The delegation data to create.
 * @returns {Promise<any>} A promise that resolves to the created delegation data.
 */
export const createProfileDelegation = async (delegationData) => {
  const response = await api.post("/my-profile/delegation", delegationData);
  return response.data;
};

/**
 * Get corporate information for the current user.
 *
 * @returns {Promise<any>} A promise that resolves to the user's corporate information.
 */
export const getProfileCorporate = async () => {
  const response = await api.get("/my-profile/corporate");
  return response.data;
};

/**
 * Create a card request for current user with optional shipping details.
 *
 * @param {object} cardRequestData - The card request data with optional shipping details.
 * @returns {Promise<any>} A promise that resolves to the created card request data.
 */
export const createProfileCardRequest = async (cardRequestData) => {
  const response = await api.post("/my-profile/card-request", cardRequestData);
  return response.data;
};
