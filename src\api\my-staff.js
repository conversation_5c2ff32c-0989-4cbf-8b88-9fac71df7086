import api from "./";

/**
 * Get people who report directly to the logged-in user.
 *
 * @returns {Promise<any>} A promise that resolves to the direct reports data.
 */
export const getDirectReports = async () => {
  const response = await api.get("/my-staff/direct-reports");
  return response.data;
};

/**
 * Get people who report to the logged-in user and their subordinates (hierarchical reporting structure).
 *
 * @returns {Promise<any>} A promise that resolves to the organization hierarchy data.
 */
export const getMyOrganization = async () => {
  const response = await api.get("/my-staff/my-organization");
  return response.data;
};
