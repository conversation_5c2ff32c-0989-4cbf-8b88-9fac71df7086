import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import ImageCapture from '../../Components/Global/ImageAndCamera/ImageCaptureForForm';
import Button from '../../Components/Global/Button';
import Input from '../Global/Input/Input';
import { IoIosArrowBack } from 'react-icons/io';
import CustomDropdown from '../../Components/Global/CustomDropdown';
import DateInput from '../Global/Input/DateInput';
import { createWatchlist } from '../../api/watchList'; // adjust path as needed

const AddWatchlistForm = () => {
  const navigate = useNavigate();

  const initialFormData = {
    firstName: '',
    middleName: '',
    lastName: '',
    suffix: '',
    dob: '',
    email: '',
    phoneNumber: '',
    expiry_date: '',
    status: 'Active',
    hostMarket: '',
    address: '',
  };

  const [formData, setFormData] = useState(initialFormData);
  const [image, setImage] = useState(null);
  const [loading, setLoading] = useState(false);

  // Reset form when component mounts to prevent caching issues
  useEffect(() => {
    setFormData(initialFormData);
    setImage(null);
    setLoading(false);
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const validateForm = () => {
    if (!formData.firstName.trim()) {
      toast.error('Please enter the First Name');
      return false;
    }
    if (!formData.lastName.trim()) {
      toast.error('Please enter the Last Name');
      return false;
    }
    return true;
  };

  const handleSave = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    // Helper function to format date for API
    const formatDateForAPI = (date) => {
      if (!date) return null;
      if (date instanceof Date) {
        return date.toISOString().split('T')[0]; // YYYY-MM-DD format
      }
      if (typeof date === 'string' && date.trim() === '') {
        return null;
      }
      return date;
    };

    const payload = {
      first_name: formData.firstName,
      middle_name: formData.middleName,
      last_name: formData.lastName,
      suffix: formData.suffix,
      date_of_birth: formatDateForAPI(formData.dob),
      email: formData.email,
      phone: formData.phoneNumber,
      expiry_date: formatDateForAPI(formData.expiry_date),
      host: formData.hostMarket,
      address: formData.address,
      status: formData.status === 'Active' ? 1 : 0,
      image: image || 'demo',
    };

    setLoading(true);
    try {
      await createWatchlist(payload);
      toast.success('Observation added successfully!');
      navigate('/observation-roster', { state: { newWatchlistEntry: payload } });
    } catch (error) {
      const errorMessage = error.response?.data?.errors?.[0]?.message || 
                           error.response?.data?.message || 
                           'Failed to add observation. Please try again.';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="flex items-center gap-2 pl-24 pt-20 text-[#4F2683]">
        <div
          className="flex items-center gap-1 cursor-pointer"
          onClick={() => navigate('/observation-roster')}
        >
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">Add Observation Roster</h2>
        </div>
      </div>

      <div className="p-6 pl-20">
        <form className="space-y-4" onSubmit={handleSave}>
          <div className="w-full bg-white shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-[10px] p-4 border">
            <div className="pb-12">
              <ImageCapture onImageCaptured={setImage} onImageUploaded={setImage} />
            </div>

            {[
              { label: 'First Name *', type: 'text', name: 'firstName', placeholder: 'First Name' },
              { label: 'Middle Name', type: 'text', name: 'middleName', placeholder: 'Middle Name' },
              { label: 'Last Name *', type: 'text', name: 'lastName', placeholder: 'Last Name' },
              { label: 'Suffix', type: 'text', name: 'suffix', placeholder: 'e.g. Jr, Sr, III' },
            ].map(({ label, type, name, placeholder }, idx) => (
              <div key={idx} className="flex items-center gap-4 py-2">
                <label className="w-1/3">{label}</label>
                <Input
                  type={type}
                  name={name}
                  value={formData[name]}
                  onChange={handleInputChange}
                  placeholder={placeholder}
                />
              </div>
            ))}

            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Date of Birth</label>
              <DateInput
                name="dob"
                value={formData.dob}
                onChange={(date) => setFormData(prev => ({ ...prev, dob: date }))}
                placeholder="MMM-DD-YYYY"
                className="w-full"
              />
            </div>

            {[
              { label: 'Email', type: 'email', name: 'email', placeholder: '<EMAIL>' },
              { label: 'Phone', type: 'tel', name: 'phoneNumber', placeholder: '************' },
            ].map(({ label, type, name, placeholder }, idx) => (
              <div key={idx} className="flex items-center gap-4 py-2">
                <label className="w-1/3">{label}</label>
                <Input
                  type={type}
                  name={name}
                  value={formData[name]}
                  onChange={handleInputChange}
                  placeholder={placeholder}
                />
              </div>
            ))}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Expiration Date</label>
              <DateInput
                name="expiry_date"
                value={formData.expiry_date}
                onChange={(date) => setFormData(prev => ({ ...prev, expiry_date: date }))}
                placeholder="MMM-DD-YYYY"
                className="w-full"
                allowFutureYears={true}
              />
            </div>
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Status</label>
              <CustomDropdown
                placeholder="Select Status"
                options={['Active', 'Inactive']}
                value={formData.status}
                rounded="rounded-md"
                bgColor="bg-[white]"
                textColor="text-gray-700"
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
                className="w-full h-11"
                onSelect={(value) => setFormData(prev => ({ ...prev, status: value }))}
              />
            </div>

            {[
              { label: 'Host/Market', type: 'text', name: 'hostMarket', placeholder: 'Enter host/market location' },
              { label: 'Address', type: 'textarea', name: 'address', placeholder: 'Enter full address' },
            ].map(({ label, type, name, placeholder }, idx) => (
              <div key={idx} className="flex items-center gap-4 py-2">
                <label className="w-1/3">{label}</label>
                {type === 'textarea' ? (
                  <textarea
                    name={name}
                    value={formData[name]}
                    onChange={handleInputChange}
                    className="border rounded-md w-full p-2 focus:ring-1 outline-none focus:ring-[#4F2683] h-20"
                    placeholder={placeholder}
                  />
                ) : (
                  <Input
                    type={type}
                    name={name}
                    value={formData[name]}
                    onChange={handleInputChange}
                    placeholder={placeholder}
                  />
                )}
              </div>
            ))}
          </div>

          <div className="flex justify-center gap-1 mt-6">
            <Button
              type="cancel"
              buttonType="button"
              label="Cancel"
              onClick={() => navigate('/observation-roster')}
            />
            <Button type="primary" buttonType="submit" label={loading ? 'Saving...' : 'Save'} disabled={loading} />
          </div>
        </form>
      </div>
    </>
  );
};

export default AddWatchlistForm;
