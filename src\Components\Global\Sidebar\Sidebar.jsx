import React, { useState, useEffect, useRef } from "react";
import ReactDOM from "react-dom";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { AiOutlineRight, AiOutlineLeft, AiOutlineDown } from "react-icons/ai";
import { PiPowerBold } from "react-icons/pi";
import Observation from "../../../Images/Menu/Observation-Roster.svg";
import { useDispatch } from "react-redux";
import { logoutUser } from "../../../redux/authSlice";
import { setSelectedFacility } from "../../../redux/facilitySlice"; // Import the action to reset facility
import { IoChatboxEllipses } from "react-icons/io5";
import { HiMiniQuestionMarkCircle } from "react-icons/hi2";
import patientImg from "../../../Images/patient.png";
import { RiSettings4Fill } from "react-icons/ri";
import AppointmentIcn from "../../../Images/Menu/Appointment-hub.svg";
import Facility from "../../../Images/Menu/Facility.svg";
import PatientHub from "../../../Images/Menu/PatientHub.svg";
import ValidationTask from "../../../Images/Menu/Validation-Hub.svg";
import identity from "../../../Images/Menu/Identity-Hub.svg";
import RequestHub from "../../../Images/Menu/Request-Hub.svg";
import TaskHub from "../../../Images/Menu/Task-Hub.svg";
import temporary from "../../../Images/Menu/Temporary-Card.svg";
import Badging from "../../../Images/Badging.svg";
import myapp from "../../../Images/Myapp.svg";
import Home from "../../../Images/Menu/Home.svg";
import Configuration from "../../../Images/SubMenu/ValidationHub/configration.svg";
import Runs from "../../../Images/SubMenu/ValidationHub/Run.svg";
import Task from "../../../Images/SubMenu/ValidationHub/Task.svg";
import Area from "../../../Images/SubMenu/PrivilegeHub/Access.svg";
import Privilege from "../../../Images/Menu/Privilege-Hub.svg";
import Hub from "../../../Images/Menu/Validation-Hub.svg";
import Dappointment from "../../../Images/SubMenu/Appointmenthub/Doctorvisit.svg";
import Inappointment from "../../../Images/SubMenu/Appointmenthub/Inpatentvisit.svg";
import MProfile from "../../../Images/SubMenu/MyApp/Sm-profile.svg";
import MTeam from "../../../Images/SubMenu/MyApp/Sm-Staff.svg";
import Mvisit from "../../../Images/SubMenu/MyApp/Sm-Visits.svg";
import Mguest from "../../../Images/SubMenu/MyApp/Sm-Guests.svg";
import Mrequest from "../../../Images/SubMenu/MyApp/Sm-Request.svg";
import Maudit from "../../../Images/SubMenu/MyApp/Sm-Audits.svg";
import Mtask from "../../../Images/SubMenu/MyApp/Sm-Task.svg";
import Marea from "../../../Images/SubMenu/PrivilegeHub/Access.svg";
import reception from "../../../Images/SubMenu/AllGuests/Reception-Desk.svg";
import allguest from "../../../Images/SubMenu/AllGuests/All-Guests.svg";
import allevent from "../../../Images/SubMenu/AllGuests/All-Events.svg";
import mguest from "../../../Images/Guest.svg";
import Tooltip from "../../Tooltip";

const Sidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Sidebar states
  const [hovered, setHovered] = useState({ label: null, x: 0, y: 0 });
  const [isExpanded, setIsExpanded] = useState(false);
  const [profileDropdown, setProfileDropdown] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const settingsRef = useRef(null);
const [showDownArrow, setShowDownArrow] = useState(true);
const sidebarScrollRef = useRef(null);

const handleSidebarScroll = () => {
  const el = sidebarScrollRef.current;
  if (!el) return;

  const isAtBottom = el.scrollHeight - el.scrollTop <= el.clientHeight + 1;
  setShowDownArrow(!isAtBottom);
};

useEffect(() => {
  handleSidebarScroll();
}, []);


  useEffect(() => {
    function handleClickOutside(e) {
      if (settingsRef.current && !settingsRef.current.contains(e.target)) {
        setShowSettings(false);
      }
    }
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  const baseItemClasses = `
    flex items-center
    h-8 px-2 rounded-lg
    hover:bg-purple-200 transition-all
  `;

  // Dropdown states for menu items with sub-items
  const [dropdownStates, setDropdownStates] = useState({
    access: false,
    appointment: false,
    validation: false,
    myApp: false,
    guesthun: false, 
  });

  const profileRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(e) {
      if (profileRef.current && !profileRef.current.contains(e.target)) {
        setProfileDropdown(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Logout handler
 const handleLogout = async () => {
  try {
    await dispatch(logoutUser());
    sessionStorage.setItem("justLoggedOut", "true");
    localStorage.removeItem("selectedFacility");
    dispatch(setSelectedFacility({ id: null, name: "Select Facility" }));
    navigate("/login");
  } catch (error) {
    console.error("Logout failed: ", error);
  }
};

  // Sidebar toggle
  const toggleExpand = () => setIsExpanded(!isExpanded);
  const closeSidebar = () => setIsExpanded(false);
  const [isHovered, setIsHovered] = useState(false);

  // Dropdown toggle handler
  const toggleDropdown = (key) => {
    setDropdownStates((prev) => {
      const updatedStates = Object.keys(prev).reduce((acc, currKey) => {
        acc[currKey] = currKey === key ? !prev[currKey] : false;
        return acc;
      }, {});
      return updatedStates;
    });
  };

  // Collapse sidebar on route change
  useEffect(() => {
    setIsExpanded(false);
  }, [location.pathname]);

  // Menu items configuration
  const menuItems = [
    { label: "Home", icon: Home, path: "/home" },
 { label: "My Task", path: "/my-task", icon: Mtask },
    {
      key: "appointment",
      label: "Appointment Hub",
      icon: AppointmentIcn,
      subItems: [
        { label: "Inpatient Visit", path: "/", icon: Inappointment },
        { label: "Doctors Appointment", path: "/doctors-appointment", icon: Dappointment },
      ],
    },

    {
      key: "guesthun",
      label: "Guest Hub",
      icon: mguest,
      subItems: [
        { label: "Reception Desk", path: "/reception-Desk", icon: reception },
         { label: "All Guests", path: "/my-guests", icon: allguest },
        { label: "All Events", path: "/My-Events", icon: allevent },
      ],
    },

    {
      key: "myApp",
      label: "My App",
      icon: myapp,
      subItems: [
        { label: "My Profile", path: "/my-profile", icon: MProfile },
        { label: "My Staff", path: "/my-staff", icon: MTeam },
        // { label: "My Events", path: "/My-Events", icon: Mvisit },
        // { label: "My Guests", path: "/my-guests", icon: Mguest },
        { label: "My Request", path: "/request", icon: Mrequest },
        { label: "My Audits", path: "/my-audits", icon: Maudit },
        // { label: "My Task", path: "/my-task", icon: Mtask },
        { label: "My Access Areas", path: "/my-areas", icon: Marea },
      ],
    },
    {
      key: "access",
      label: "Privilege Hub",
      icon: Privilege,
      subItems: [
        { label: "Access Area", path: "/access-areas", icon: Area },
        { label: "Access Group", path: "/access-group", icon: Area },
      ],
    },
    {
      key: "validation",
      label: "Validation Hub",
      icon: ValidationTask,
      subItems: [
        { label: "Validation Task", path: "/validation-task", icon: Task },
        { label: "Validation Runs", path: "/validation-run", icon: Runs },
        { label: "Configuration", path: "/validation-configuration", icon: Configuration },
      ],
    },
    { label: "Credential Hub", icon: Badging, path: "/credential" },
    { label: "Temporary Cards", icon: temporary, path: "/temporary-cards" },
    { label: "Observation Roster", icon: Observation, path: "/observation-roster"  },
    { label: "Facility", icon: Facility, path: "/facility" },
    {
      key: "patientHub",
      label: "Patient Hub",
      icon: PatientHub,
       path: "/patient-hub",
     
    },
    { label: "Identity Hub", icon: identity, path: "/identity-hub" },
    { label: "Request Hub", icon: RequestHub, path: "/request-hub" },
    // { label: "Task Hub", icon: TaskHub, path: "/task-hub" },
  ];

  // Render menu items
  const renderMenuItems = () =>
    menuItems.map((item) => {
      const isActive =
        location.pathname === item.path ||
        (item.subItems &&
          item.subItems.some((subItem) => location.pathname === subItem.path));
      if (item.subItems) {
        return (

          <li
            key={item.key}
            className="relative"
            onMouseEnter={() => {
              if (!isExpanded) {
                setDropdownStates((prev) => {
                  const updatedStates = Object.keys(prev).reduce((acc, currKey) => {
                    acc[currKey] = currKey === item.key;
                    return acc;
                  }, {});
                  return updatedStates;
                });
              }
            }}
            onMouseLeave={() => !isExpanded && setDropdownStates((prev) => ({ ...prev, [item.key]: false }))}
          >
            <div
              className={`flex text-sm items-center rounded-lg ${isExpanded ? " justify-between" : "w-8 h-8 px-1 py-2 justify-center "
                } ${isActive ? "bg-purple-100 text-purple-700" : "text-gray-700"
                } hover:bg-purple-200 transition-all ${isExpanded ? "px-2 py-2 h-8" : ""}`}
              onClick={() => isExpanded && toggleDropdown(item.key)}
            >
              <div className="flex items-center gap-2">
                {isExpanded ? (
                  <img src={item.icon} alt={item.label} className="w-6 h-6   object-contain" />
                ) : (
                  <div>
                    <img src={item.icon} alt={item.label} className="w-6 h-6 object-contain" />
                  </div>
                )}
                {isExpanded &&         <span className="whitespace-nowrap overflow-hidden text-ellipsis transition-all duration-300 opacity-100 flex-1 ml-2">
                 {item.label}</span>}
              </div>

              {isExpanded && item.subItems && (
                dropdownStates[item.key] ? (
                  <AiOutlineDown className="absolute right-1 transition duration-300" />
                ) : (
                  <AiOutlineRight className="absolute right-1 transition duration-300" />
                )
              )}
            </div>
            {dropdownStates[item.key] && (
              <ul
                onMouseEnter={() => {
                  if (!isExpanded) {
                    setDropdownStates((prev) => ({ ...prev, [item.key]: true }));
                  }
                }}
                onMouseLeave={() => {
                  if (!isExpanded) {
                    setDropdownStates((prev) => ({ ...prev, [item.key]: false }));
                  }
                }}
                className={`${isExpanded ? "space-y-2" : "absolute top-[-4rem] left-full mt-0 w-52"} 
                                bg-white rounded-lg shadow-lg z-30 py-2`}
              >
                <div>
                  {isExpanded ? (
                    ""
                  ) : (
                    <>
                      <p className="ms-2 text-sm p-1">{item.label}</p>
                      <hr className="mx-2 my-1" />
                    </>
                  )}
                </div>

                {item.subItems.map((subItem) => (
                  <li key={subItem.path}>
                    <NavLink
                      to={subItem.path}
                      onClick={() => {
                        setDropdownStates((prev) => ({ ...prev, [item.key]: false }));
                      }}
                      className={` px-2 h-6 flex items-center gap-2 text-xs ${location.pathname === subItem.path
                        ? "bg-purple-100 text-purple-700"
                        : "text-gray-700"
                        } hover:bg-purple-200 transition-all`}
                    >
                      {/* Render icon if it's image */}
                      {typeof subItem.icon === "string" ? (
                        <img src={subItem.icon} alt={subItem.label} className="w-4 h-4 object-contain" />
                      ) : (
                        // Render icon if it's a component
                        subItem.icon && React.createElement(subItem.icon, { className: "w-4 h-4 text-[#4F2386]" })
                      )}
                      <span>{subItem.label}</span>
                    </NavLink>
                  </li>
                ))}
              </ul>
            )}
          </li>
        );
      }
      return (
        <li key={item.label}
          className={`${isExpanded ? "" : "flex justify-center relative"}`}
          onMouseEnter={e => {
            const rect = e.currentTarget.getBoundingClientRect();
            setHovered({
              label: item.label,
              x: rect.right + 8,
              y: rect.top + rect.height / 2
            });
          }}
          onMouseLeave={() => setHovered({ label: null, x: 0, y: 0 })} >
          <NavLink
            to={item.path}
            className={`flex items-center  rounded-lg text-sm ${isExpanded ? "" : "w-8 h-8 py-2 px-1 justify-between "
              } ${location.pathname === item.path ? "bg-purple-100 text-gray-700 " : "text-gray-700"
              } hover:bg-purple-200 transition-all  ${isExpanded ? "px-2 py-2 h-8 whitespace-nowrap" : ""}`}
          >
            <div className={`group ${isExpanded ? "relative" : "absolute"}`}>
              <img src={item.icon} alt={item.label} className="w-6 h-6" />
              {/* <div className="left-full top-1/2 -translate-y-1/2 ml-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] opacity-90 !absolute !font-medium"
              >
                {item.label}
              </div> */}
            </div>
            {isExpanded && <span className="ml-4">{item.label}</span>}
          </NavLink>
          {/* {item.label === "Home" && (
            <div
              className={`absolute  ${isExpanded ? "w-auto left-[7px] mb-8 right-[7px]  after:content-[''] after:block after:border-t  after:border-gray-300" : " left-[-7px] mb-8 right-[-7px] my-1 mt-7 after:content-[''] after:block after:border-t after:border-gray-300"} `}
            />
          )} */}

        </li>
      );
    });
         <AiOutlineDown className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 text-[#4F2386] text-xs" />
  const isAnyDropdownOpen = Object.values(dropdownStates).some((val) => val);
  const mainContent = (
    <div>
      {isExpanded && (
        <div
          className="fixed inset-x-0 top-[56px] bottom-0 bg-black bg-opacity-50 z-10"
          onClick={closeSidebar}
        ></div>
      )}
      {profileDropdown && (
        <div
          className="fixed inset-x-0 top-[56px] bottom-0 bg-opacity-50 z-10"
          onClick={() => setProfileDropdown(false)}
        ></div>
      )}
      <div
        className={`sidebar-container fixed z-20 top-14 bg-white flex flex-col rounded-tr-lg rounded-br-lg border shadow-lg transition-width duration-400 ${isExpanded ? "w-52" : "w-14"} h-[calc(100vh-56px)]`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >

        {/* Scrollable menu area */}
        <div
  ref={sidebarScrollRef}
  onScroll={handleSidebarScroll}
  className={`flex-1 overflow-y-auto hide-scrollbar mt-2 flex flex-col ${isExpanded ? "justify-start items-center" : "justify-start items-center"}`}
>


          {!isExpanded && isHovered && (
            <button
              className="absolute top-[4.1rem] -right-4 text-white transition-all"
              onClick={toggleExpand}

            >
              <AiOutlineRight
                size={24}
                className="bg-white border shadow-md text-[#4F2683] rounded-full p-1 hover:bg-[#4F2683] hover:text-white"
              />
            </button>
          )}
          {isExpanded && (
            <button
              className="absolute top-[4.1rem] -right-4 text-white transition-all"
              onClick={toggleExpand}
            >
              <AiOutlineLeft size={24} className="bg-[#4F2683] rounded-full p-1" />
            </button>
          )}
          <ul className={`${!isExpanded && isAnyDropdownOpen ? "absolute " : ""} space-y-2 pb-14`}>
            {/** first item: “Home” **/}
            {renderMenuItems().slice(0, 2)}

            {/** always‑visible divider **/}
            <li className="divider"></li>

            {/** the rest of the menu **/}
            {renderMenuItems().slice(2)}
          </ul>
        </div>
   {showDownArrow && (
  <AiOutlineDown 
    className={`absolute  ${isExpanded ? "left-[13px] bottom-[60px]" : "left-[14px] bottom-[40px]"} animate-bounce rounded-lg bg-[#E9D5FF] p-1 h-7 w-7 text-[#4F2386] text-xs transition-all duration-300`} 
  />
)}

        {/* Fixed Profile Section */}
        <div ref={profileRef}>
          <div className="fixed bottom-0  border-t pb-3 bg-white">
            <div
              className={`absolute bottom-0 left-full mt-0 pb-2 bg-white  shadow-lg z-30${profileDropdown ? "bg-purple-100 text-[15px]urple-700" : "text-gray-700"
                }`}
              onClick={() => setProfileDropdown(!profileDropdown)}
            >
              {isExpanded ? (
                <div className="flex gap-1 items-center w-52 p-2 justify-between">
                  <div className="flex flex-col">
                    <p className="text-[#4F2386] text-[12px] font-semibold">John Smith</p>
                    <p className="text-[12px] font-normal text-[#7C7C7C]"><EMAIL></p>
                  </div>
                  <div className="w-7 h-7 flex items-center">
                    <img className="rounded-full" src={patientImg} alt="User" />
                  </div>
                </div>
              ) : (
                <div className="w-full flex justify-center items-center pl-3">
                  <div className="w-6 h-6 rounded-full overflow-hidden">
                    <img src={patientImg} alt="User" />
                  </div>
                </div>
              )}
              {profileDropdown && (
                <ul className="absolute bottom-2 left-16 space-y-2  bg-white rounded-lg shadow-lg py-2 w-52 z-30">
                  <li>
                    <NavLink
                      to="/profile"
                      className="block px-2 py-1 hover:bg-purple-200 transition-all items-center justify-center"
                    >
                      <div className="flex items-center gap-1">
                        <div className="flex flex-col">
                          <p className="text-[#4F2386] text-[12px] ">John Smith</p>
                          <p className="text-[12px] font-normal text-[#7C7C7C]"><EMAIL></p>
                        </div>
                        <div className=" w-10 items-center flex">
                          <img className="rounded-full" src={patientImg} alt="User" />
                        </div>
                      </div>
                    </NavLink>
                  </li>
                  <hr className="mx-3"></hr>
                  <li ref={settingsRef} className="relative">
                    <div
                      className="flex items-center gap-1 cursor-pointer px-2 py-1 hover:bg-purple-200"
                      onClick={e => {
                        e.stopPropagation();               // ← prevent the document listener
                        setShowSettings(prev => !prev);
                      }}
                    >
                      <RiSettings4Fill className="h-6 w-6 text-[#4F2386]" />
                      <span className="text-[#7C7C7C]">Settings</span>
                    </div>

                    {showSettings && (
                      <div className="absolute left-full top-0 ml-2 bg-white shadow-lg rounded-md p-3 w-40 z-10">
                        <div className="text-[#4F2386] font-semibold mb-2">Language</div>
                        <ul className="space-y-1 text-[#7C7C7C]">
                          <li className="cursor-pointer hover:text-black">English</li>
                          <li className="cursor-pointer hover:text-black">Spanish</li>
                        </ul>
                      </div>
                    )}
                  </li>
                  <li>
                    <NavLink
                      // to="/"
                      // onClick={}
                      className={`block px-2 py-1 text-[12px  `}
                    >
                      <div className="flex items-center gap-1">
                        <IoChatboxEllipses className="text-[#4F2386] h-[25px] w-[25px]" />
                        <span className="text-[#7C7C7C]">
                          Chat With Us
                        </span>
                      </div>
                    </NavLink>
                  </li>
                  <li>
                    <NavLink
                      // to="/"
                      // onClick={}
                      className={`block px-2 py-1 text-[12px  `}
                    >
                      
                      <div className="flex items-center gap-1">
                        <HiMiniQuestionMarkCircle className="text-[#4F2386] h-[25px] w-[25px]" />
                        <span className="text-[#7C7C7C]">
                          What's New?
                        </span>
                      </div>
                    </NavLink>
                  </li>
                  <hr className="mx-3" />
                  <li>
                    <button
                      onClick={handleLogout}
                      className="w-full text-[12px] flex items-center gap-1 px-2 py-1 hover:bg-purple-200 transition-all"
                    >
                      <PiPowerBold className="text-[#4F2386] h-[25px] w-[25px]" />
                      <span className="text-[#7C7C7C]">Logout</span>
                    </button>
                  </li>
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
  //   const labelTooltip = (
  //     <>
  //   <div className="left-full top-1/2 -translate-y-1/2 ml-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] opacity-90 !absolute !font-medium"
  //               >
  //                 {/* {itme.label} */}
  //               </div>
  //                 </>
  // )
  //   const targetTooltip = document.getElementById("portal-handler-div");
  const portalRoot = document.getElementById("portal-handler-div");

  return (
    <>
      {mainContent}
      {hovered.label && !isExpanded && portalRoot &&
        ReactDOM.createPortal(
          <Tooltip
            label={hovered.label}
            style={{
              position: "fixed",
              top: hovered.y,
              left: hovered.x,
              transform: "translateY(-50%)"
            }}
          />,
          portalRoot
        )
      }
    </>
  );
};

export default Sidebar;