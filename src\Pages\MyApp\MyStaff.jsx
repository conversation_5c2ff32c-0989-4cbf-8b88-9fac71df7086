import React, { useState, useMemo, useEffect, useCallback } from "react";
import GenericTable from "../../Components/GenericTable";
import { FilterButtons } from "../../Components/GenericTable";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import newWindow from "../../Images/new-window.svg";
import { getDirectReports, getMyOrganization } from "../../api/my-staff";
// Example data for the table
const initialTeams = [
  {
    id: 1,
    name: "<PERSON><PERSON> Khiladiye",
    uid: "*********",
    type: "Non Billable",
    company: "Oracle",
    organization: "Service Contractor",
    jobTitle: "",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
  {
    id: 2,
    name: "<PERSON>alk<PERSON>",
    uid: "*********",
    type: "Non Billable",
    company: "Oracle",
    organization: "Service Contractor",
    jobTitle: "",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
  {
    id: 3,
    name: "<PERSON>",
    uid: "*********",
    type: "Contractor",
    company: "Oracle",
    organization: "-",
    jobTitle: "-",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
  {
    id: 4,
    name: "Piney Spiro",
    uid: "*********",
    type: "Non Billable",
    company: "Oracle",
    organization: "-",
    jobTitle: "-",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
  {
    id: 5,
    name: "Picklyman Proto",
    uid: "*********",
    type: "Non Billable",
    company: "Oracle",
    organization: "-",
    jobTitle: "-",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
];

const MyStaff = () => {
  const { t } = useTranslation();
  const [teams, setTeams] = useState(initialTeams);
  const [apiData, setApiData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("directReports");
  const [hoveredRow, setHoveredRow] = useState(null);

  const filterOptions = [
    { value: "directReports", label: t("my_staff.filter.direct_reports") },
    { value: "myOrg", label: t("my_staff.filter.my_org") },
  ];

  // Fetch data based on active tab
  const fetchStaffData = useCallback(async () => {
    setLoading(true);
    try {
      let response;
      if (activeTab === "directReports") {
        response = await getDirectReports();
      } else {
        response = await getMyOrganization();
      }

      // Transform API response to match table structure
      const transformedData = response.data?.map((item, index) => ({
        id: item.id || index + 1,
        name: item.name || item.full_name || "",
        uid: item.uid || item.employee_id || "",
        type: item.type || item.employee_type || "Non Billable",
        company: item.company || "Oracle",
        organization: item.organization || item.department || "Service Contractor",
        jobTitle: item.job_title || item.position || "",
        manager: item.manager || item.manager_name || "",
        expirationDate: item.expiration_date || "Dec-31-2025",
        status: item.status || "Approved",
        image: item.image || item.profile_image || "",
      })) || [];

      setApiData(transformedData);
    } catch (error) {
      console.error("Error fetching staff data:", error);
      toast.error(`Error fetching ${activeTab === "directReports" ? "direct reports" : "organization"} data`);
      // Fallback to initial data on error
      setApiData(initialTeams);
    } finally {
      setLoading(false);
    }
  }, [activeTab]);

  useEffect(() => {
    fetchStaffData();
  }, [fetchStaffData]);

  // Table columns
  const columns = [
    {
      name: t("my_staff.name"),
      selector: (row) => row.name,
      sortable: true,
      cell: (row, index) => (
        <div
          className="relative flex items-center space-x-3 cursor-pointer group w-full"
          onMouseEnter={() => setHoveredRow(index)} // Use setHoveredRow here
          onMouseLeave={() => setHoveredRow(null)}
          onClick={() => window.open(`/team-details`, "_blank")}
        >
          <img src={row.image} alt={row.name} className="w-8 h-8 rounded-full" />
          <span>{row.name}</span>
          {hoveredRow === index && (
            <img className="w-4 h-4" src={newWindow} alt={t("my_staff.open")} />
          )}
        </div>
      ),
    },
    {
      name: t("my_staff.uid"),
      selector: (row) => row.uid,
    },
    {
      name: t("my_staff.type"),
      selector: (row) => row.type,
    },
    {
      name: t("my_staff.company"),
      selector: (row) => row.company,
    },
    {
      name: t("my_staff.organization"),
      selector: (row) => row.organization,
    },
    {
      name: t("my_staff.job_title"),
      selector: (row) => row.jobTitle,
    },
    {
      name: t("my_staff.manager"),
      selector: (row) => row.manager,
    },
    {
      name: t("my_staff.expiration_date"),
      selector: (row) => row.expirationDate,
    },
    {
      name: t("my_staff.status"),
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full ${
            row.status.toLowerCase() === "approved"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {t(`my_staff.status_${row.status.toLowerCase()}`)}
        </span>
      ),
    },
  ];

  const filteredTeams = useMemo(() => {
    // Use API data if available, otherwise fallback to static data
    const dataToUse = apiData.length > 0 ? apiData : teams;

    if (activeTab === "directReports") {
      return dataToUse;
    } else {
      return dataToUse;
    }
  }, [apiData, teams, activeTab]);

  const handleAddTeam = () => {
    alert("Add Team Member Clicked!");
  };

  return (
    <div className="pl-24 mt-20 pr-8 h-full">
      {/* Header */}
      <div className="text-[24px] font-normal text-[#4F2683] mb-4">
        <h3>{t("my_staff.title")}</h3>
      </div>

      {/* Filter Buttons */}
      <FilterButtons
        filter={activeTab}
        onFilterChange={setActiveTab}
        filterOptions={filterOptions}
      />

      {/* Table Section */}
      <div className="mt-4 bg-white rounded-[10px]">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="text-lg text-gray-600">Loading...</div>
          </div>
        ) : (
          <GenericTable
            title={t("my_staff.title")}
            showAddButton={false}
            columns={columns}
            data={filteredTeams}
            onAdd={handleAddTeam}
          />
        )}
      </div>
    </div>
  );
};

export default MyStaff;
