/**
 * Test file to verify the observation roster fixes
 */

// Test date parsing functionality
const testDateParsing = () => {
  console.log('Testing date parsing functionality...');
  
  // Test MMM-DD-YYYY format
  const testDate1 = 'Jan-15-2024';
  const testDate2 = 'Dec-31-2025';
  const testDate3 = '2024-01-15'; // YYYY-MM-DD format
  
  console.log('Test dates:', { testDate1, testDate2, testDate3 });
  
  // Test date formatting for display
  const formatDateForDisplay = (date) => {
    if (!date) return '';
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = months[d.getMonth()];
    const day = String(d.getDate()).padStart(2, '0');
    const year = d.getFullYear();
    
    return `${month}-${day}-${year}`;
  };
  
  console.log('Formatted dates:', {
    date1: formatDateForDisplay(new Date('2024-01-15')),
    date2: formatDateForDisplay(new Date('2025-12-31')),
    date3: formatDateForDisplay(new Date())
  });
};

// Test validation functionality
const testValidation = () => {
  console.log('Testing validation functionality...');
  
  const validateForm = (formData) => {
    if (!formData.firstName.trim()) {
      console.log('Validation failed: First Name is required');
      return false;
    }
    if (!formData.lastName.trim()) {
      console.log('Validation failed: Last Name is required');
      return false;
    }
    console.log('Validation passed: Only required fields checked');
    return true;
  };
  
  // Test cases
  const testCases = [
    { firstName: 'John', lastName: 'Doe', email: '' }, // Should pass
    { firstName: '', lastName: 'Doe', email: '<EMAIL>' }, // Should fail
    { firstName: 'John', lastName: '', email: '<EMAIL>' }, // Should fail
    { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' }, // Should pass
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`Test case ${index + 1}:`, testCase);
    validateForm(testCase);
  });
};

// Test API payload formatting
const testAPIPayload = () => {
  console.log('Testing API payload formatting...');
  
  const formatDateForAPI = (date) => {
    if (!date) return null;
    if (date instanceof Date) {
      return date.toISOString().split('T')[0]; // YYYY-MM-DD format
    }
    if (typeof date === 'string' && date.trim() === '') {
      return null;
    }
    return date;
  };
  
  const testDates = [
    new Date('2024-01-15'),
    '',
    null,
    undefined,
    '2024-12-31'
  ];
  
  testDates.forEach((date, index) => {
    console.log(`Date ${index + 1}:`, date, '->', formatDateForAPI(date));
  });
};

// Run tests
if (typeof window !== 'undefined') {
  // Browser environment
  console.log('Running observation roster fixes tests...');
  testDateParsing();
  testValidation();
  testAPIPayload();
} else {
  // Node.js environment
  module.exports = {
    testDateParsing,
    testValidation,
    testAPIPayload
  };
}
