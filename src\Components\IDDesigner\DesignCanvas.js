import React, { useRef, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Stage, Layer, Rect, Group } from 'react-konva';
import { Box } from '@mui/material';

import DesignElement from './Elements/DesignElement';
import SelectionBox from './SelectionBox';
import Ruler from './Ruler';

import {
  updateElement,
  selectElement,
  selectMultipleElements,
  clearSelection,
  updateCanvasConfig,
} from '../../redux/idDesignerSlice';
import { getGridSizeInPixels } from '../../utils/unitConversion';

const DesignCanvas = () => {
  const dispatch = useDispatch();
  const stageRef = useRef();
  const [stageSize, setStageSize] = useState({ width: 800, height: 600 });
  const [selectionRect, setSelectionRect] = useState(null);
  const [isDragging, setIsDragging] = useState(false);

  // Touch handling state
  const [lastTouchDistance, setLastTouchDistance] = useState(0);
  const [lastTouchCenter, setLastTouchCenter] = useState({ x: 0, y: 0 });
  const [touchStartTime, setTouchStartTime] = useState(0);
  const [longPressTimer, setLongPressTimer] = useState(null);

  const {
    elements,
    selectedElementIds,
    canvasConfig,
    previewMode,
  } = useSelector((state) => state.idDesigner);

  const rulerThickness = 30;
  const canvasPadding = 40; // Fixed padding for consistent positioning

  // Calculate canvas position - fixed position with padding
  const availableWidth = stageSize.width - (canvasConfig.showRulers ? rulerThickness : 0);
  const availableHeight = stageSize.height - (canvasConfig.showRulers ? rulerThickness : 0);

  const canvasX = canvasPadding;
  const canvasY = canvasPadding;

  // Generate grid background style
  const getGridBackgroundStyle = () => {
    if (!canvasConfig.showGrid) return {};

    const gridSize = getGridSizeInPixels(canvasConfig.unit, canvasConfig.dpi) * canvasConfig.scale;

    return {
      backgroundImage: `
        linear-gradient(to right, #e0e0e0 1px, transparent 1px),
        linear-gradient(to bottom, #e0e0e0 1px, transparent 1px)
      `,
      backgroundSize: `${gridSize}px ${gridSize}px`,
      backgroundPosition: `${canvasX % gridSize}px ${canvasY % gridSize}px`,
    };
  };

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const container = stageRef.current?.container();
      if (container) {
        const containerRect = container.getBoundingClientRect();
        setStageSize({
          width: containerRect.width,
          height: containerRect.height,
        });
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle keyboard controls for element movement
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Only handle arrow keys when elements are selected and not in preview mode
      if (selectedElementIds.length === 0 || previewMode) return;

      const moveDistance = e.shiftKey ? 10 : 1; // Hold Shift for larger movements
      let deltaX = 0;
      let deltaY = 0;

      switch (e.key) {
        case 'ArrowLeft':
          deltaX = -moveDistance;
          e.preventDefault();
          break;
        case 'ArrowRight':
          deltaX = moveDistance;
          e.preventDefault();
          break;
        case 'ArrowUp':
          deltaY = -moveDistance;
          e.preventDefault();
          break;
        case 'ArrowDown':
          deltaY = moveDistance;
          e.preventDefault();
          break;
        default:
          return;
      }

      // Move all selected elements
      selectedElementIds.forEach(elementId => {
        const element = elements.find(el => el.id === elementId);
        if (element) {
          const newX = Math.max(0, Math.min(canvasConfig.width - element.width, element.x + deltaX));
          const newY = Math.max(0, Math.min(canvasConfig.height - element.height, element.y + deltaY));

          dispatch(updateElement({
            id: elementId,
            properties: { x: newX, y: newY }
          }));
        }
      });
    };

    // Add event listener to document to capture keyboard events
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedElementIds, elements, canvasConfig, previewMode, dispatch]);



  // Handle stage click
  const handleStageClick = (e) => {
    if (e.target === e.target.getStage()) {
      dispatch(clearSelection());
    }
  };

  // Handle element drag
  const handleElementDragMove = (elementId, newPos) => {
    const { snapToGrid, width, height, unit, dpi } = canvasConfig;

    let updates = { ...newPos };

    // Apply grid snapping if enabled
    if (snapToGrid && (newPos.x !== undefined || newPos.y !== undefined)) {
      const gridSize = getGridSizeInPixels(unit, dpi);
      if (newPos.x !== undefined) {
        updates.x = Math.round(newPos.x / gridSize) * gridSize;
      }
      if (newPos.y !== undefined) {
        updates.y = Math.round(newPos.y / gridSize) * gridSize;
      }
    }

    // Constrain elements within canvas bounds
    const element = elements.find(el => el.id === elementId);
    if (element) {
      if (updates.x !== undefined) {
        updates.x = Math.max(0, Math.min(width - element.width, updates.x));
      }
      if (updates.y !== undefined) {
        updates.y = Math.max(0, Math.min(height - element.height, updates.y));
      }
    }

    dispatch(updateElement({
      id: elementId,
      properties: updates,
    }));
  };

  // Handle element selection
  const handleElementClick = (elementId, e) => {
    e.cancelBubble = true;
    const multiSelect = e.evt.shiftKey || e.evt.ctrlKey || e.evt.metaKey;
    dispatch(selectElement({ id: elementId, multiSelect }));
  };

  // Handle mouse down for selection rectangle
  const handleMouseDown = (e) => {
    if (e.target !== e.target.getStage()) return;

    const pos = e.target.getStage().getPointerPosition();
    setSelectionRect({
      x: pos.x,
      y: pos.y,
      width: 0,
      height: 0,
    });
    setIsDragging(true);
  };

  // Handle mouse move for selection rectangle
  const handleMouseMove = (e) => {
    if (!isDragging || !selectionRect) return;

    const pos = e.target.getStage().getPointerPosition();
    setSelectionRect({
      ...selectionRect,
      width: pos.x - selectionRect.x,
      height: pos.y - selectionRect.y,
    });
  };

  // Handle mouse up for selection rectangle
  const handleMouseUp = () => {
    if (!isDragging || !selectionRect) return;

    setIsDragging(false);

    // Find elements within selection rectangle
    const { scale, offsetX, offsetY } = canvasConfig;
    const selectionBounds = {
      x: Math.min(selectionRect.x, selectionRect.x + selectionRect.width) - offsetX,
      y: Math.min(selectionRect.y, selectionRect.y + selectionRect.height) - offsetY,
      width: Math.abs(selectionRect.width),
      height: Math.abs(selectionRect.height),
    };

    const selectedIds = elements
      .filter((element) => {
        const elementBounds = {
          x: element.x * scale,
          y: element.y * scale,
          width: element.width * scale,
          height: element.height * scale,
        };

        return (
          elementBounds.x >= selectionBounds.x &&
          elementBounds.y >= selectionBounds.y &&
          elementBounds.x + elementBounds.width <= selectionBounds.x + selectionBounds.width &&
          elementBounds.y + elementBounds.height <= selectionBounds.y + selectionBounds.height
        );
      })
      .map((element) => element.id);

    if (selectedIds.length > 0) {
      dispatch(selectMultipleElements(selectedIds));
    }

    setSelectionRect(null);
  };



  // Handle wheel for zoom - keep canvas in fixed position
  const handleWheel = (e) => {
    e.evt.preventDefault();

    const oldScale = canvasConfig.scale;
    const newScale = e.evt.deltaY > 0 ? oldScale * 0.9 : oldScale * 1.1;
    const clampedScale = Math.max(0.1, Math.min(3, newScale));

    // Update scale only, keep canvas in fixed position
    dispatch(updateCanvasConfig({
      scale: clampedScale
    }));
  };

  // Touch event handlers
  const getTouchDistance = (touch1, touch2) => {
    return Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    );
  };

  const getTouchCenter = (touch1, touch2) => {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
    };
  };

  const handleTouchStart = (e) => {
    const touches = e.evt.touches;
    setTouchStartTime(Date.now());

    if (touches.length === 1) {
      // Single touch - start long press timer
      const timer = setTimeout(() => {
        // Long press detected - show context menu or select element
        const stage = stageRef.current;
        const pointerPosition = stage.getPointerPosition();

        // Find element at touch position
        const elementAtPosition = elements.find(element => {
          return (
            pointerPosition.x >= element.x &&
            pointerPosition.x <= element.x + element.width &&
            pointerPosition.y >= element.y &&
            pointerPosition.y <= element.y + element.height
          );
        });

        if (elementAtPosition) {
          dispatch(selectElement({ id: elementAtPosition.id, multiSelect: false }));
        }
      }, 500); // 500ms for long press

      setLongPressTimer(timer);
    } else if (touches.length === 2) {
      // Two finger touch - prepare for pinch zoom
      const distance = getTouchDistance(touches[0], touches[1]);
      const center = getTouchCenter(touches[0], touches[1]);
      setLastTouchDistance(distance);
      setLastTouchCenter(center);
    }
  };

  const handleTouchMove = (e) => {
    const touches = e.evt.touches;

    // Clear long press timer on move
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    if (touches.length === 2) {
      // Pinch zoom
      e.evt.preventDefault();

      const distance = getTouchDistance(touches[0], touches[1]);
      const center = getTouchCenter(touches[0], touches[1]);

      if (lastTouchDistance > 0) {
        const scale = canvasConfig.scale * (distance / lastTouchDistance);
        const clampedScale = Math.max(0.1, Math.min(3, scale));

        dispatch(updateCanvasConfig({
          scale: clampedScale
        }));
      }

      setLastTouchDistance(distance);
      setLastTouchCenter(center);
    }
  };

  const handleTouchEnd = (e) => {
    const touches = e.evt.touches;
    const touchEndTime = Date.now();

    // Clear long press timer
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    if (touches.length === 0) {
      // All touches ended
      setLastTouchDistance(0);

      // Check for quick tap (less than 200ms)
      if (touchEndTime - touchStartTime < 200) {
        // Handle tap selection similar to click
        const stage = stageRef.current;
        const pointerPosition = stage.getPointerPosition();

        if (pointerPosition) {
          const elementAtPosition = elements.find(element => {
            return (
              pointerPosition.x >= element.x &&
              pointerPosition.x <= element.x + element.width &&
              pointerPosition.y >= element.y &&
              pointerPosition.y <= element.y + element.height
            );
          });

          if (elementAtPosition) {
            dispatch(selectElement({ id: elementAtPosition.id, multiSelect: false }));
          } else {
            dispatch(clearSelection());
          }
        }
      }
    }
  };

  return (
    <Box
      sx={{
        flex: 1,
        overflow: 'hidden',
        position: 'relative',
        cursor: isDragging ? 'crosshair' : 'default',
        bgcolor: '#fafafa',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Rulers and Canvas Container */}
      <Box
        sx={{
          display: 'flex',
          position: 'relative',
        }}
      >
        {/* Top-left corner (empty space for rulers intersection) */}
        {canvasConfig.showRulers && (
          <Box
            sx={{
              width: rulerThickness,
              height: rulerThickness,
              backgroundColor: '#f5f5f5',
              borderRight: '1px solid #ddd',
              borderBottom: '1px solid #ddd',
              flexShrink: 0,
            }}
          />
        )}

        {/* Horizontal Ruler */}
        {canvasConfig.showRulers && (
          <Box>
            <Ruler
              orientation="horizontal"
              length={availableWidth}
              scale={canvasConfig.scale}
              offset={canvasX}
              unit={canvasConfig.unit}
              dpi={canvasConfig.dpi}
              thickness={rulerThickness}
              canvasWidth={canvasConfig.width}
              canvasHeight={canvasConfig.height}
              stageWidth={availableWidth}
              stageHeight={availableHeight}
            />
          </Box>
        )}
      </Box>

      {/* Main content area with vertical ruler and canvas */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          overflow: 'hidden',
        }}
      >
        {/* Vertical Ruler */}
        {canvasConfig.showRulers && (
          <Box sx={{ flexShrink: 0 }}>
            <Ruler
              orientation="vertical"
              length={availableHeight}
              scale={canvasConfig.scale}
              offset={canvasY}
              unit={canvasConfig.unit}
              dpi={canvasConfig.dpi}
              thickness={rulerThickness}
              canvasWidth={canvasConfig.width}
              canvasHeight={canvasConfig.height}
              stageWidth={availableWidth}
              stageHeight={availableHeight}
            />
          </Box>
        )}

        {/* Canvas Area */}
        <Box
          sx={{
            flex: 1,
            position: 'relative',
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            ...getGridBackgroundStyle(),
          }}
        >
          <Stage
            ref={stageRef}
            width={availableWidth}
            height={availableHeight}
            scaleX={canvasConfig.scale}
            scaleY={canvasConfig.scale}
            x={canvasX}
            y={canvasY}
            onClick={handleStageClick}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onWheel={handleWheel}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            draggable={false}
          >
            <Layer>
              {/* Canvas Background */}
              <Rect
                x={0}
                y={0}
                width={canvasConfig.width}
                height={canvasConfig.height}
                fill={canvasConfig.background}
                stroke="#ccc"
                strokeWidth={1}
                listening={false}
              />

              {/* Clipping Group for Elements */}
              <Group
                clipX={0}
                clipY={0}
                clipWidth={canvasConfig.width}
                clipHeight={canvasConfig.height}
              >
                {/* Design Elements */}
                {[...elements]
                  .sort((a, b) => a.zIndex - b.zIndex)
                  .map((element) => (
                    <DesignElement
                      key={element.id}
                      element={element}
                      isSelected={selectedElementIds.includes(element.id)}
                      isPreviewMode={previewMode}
                      onDragMove={(newPos) => handleElementDragMove(element.id, newPos)}
                      onClick={(e) => handleElementClick(element.id, e)}
                    />
                  ))}
              </Group>

              {/* Selection Rectangle */}
              {selectionRect && (
                <Rect
                  x={selectionRect.x}
                  y={selectionRect.y}
                  width={selectionRect.width}
                  height={selectionRect.height}
                  fill="rgba(79, 38, 131, 0.05)"
                  stroke="rgba(79, 38, 131, 0.6)"
                  strokeWidth={1}
                  shadowColor="rgba(79, 38, 131, 0.15)"
                  shadowBlur={4}
                  shadowOffset={{ x: 0, y: 2 }}
                  listening={false}
                />
              )}
            </Layer>
          </Stage>

          {/* Selection Box for multi-select */}
          {selectedElementIds.length > 1 && (
            <SelectionBox
              elements={elements.filter(el => selectedElementIds.includes(el.id))}
              canvasConfig={canvasConfig}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default DesignCanvas;
