// config-overrides.js
module.exports = function override(config) {
  // Disable source maps for react-datepicker specifically
  config.module.rules.forEach(rule => {
    if (rule.oneOf) {
      rule.oneOf.forEach(oneOfRule => {
        if (oneOfRule.use && oneOfRule.use.some) {
          oneOfRule.use.forEach(use => {
            if (use.loader && use.loader.includes('source-map-loader')) {
              if (!use.exclude) {
                use.exclude = [];
              }
              if (Array.isArray(use.exclude)) {
                use.exclude.push(/node_modules\/react-datepicker/);
              } else {
                use.exclude = [use.exclude, /node_modules\/react-datepicker/];
              }
            }
          });
        }
      });
    }

    // Also check direct rules
    if (rule.use && Array.isArray(rule.use)) {
      rule.use.forEach(use => {
        if (use.loader && use.loader.includes('source-map-loader')) {
          if (!use.exclude) {
            use.exclude = [];
          }
          if (Array.isArray(use.exclude)) {
            use.exclude.push(/node_modules\/react-datepicker/);
          } else {
            use.exclude = [use.exclude, /node_modules\/react-datepicker/];
          }
        }
      });
    }
  });

  return config;
};
