import React, { useState, useMemo, useEffect } from "react";
import GenericTable from "../../../GenericTable";
import TruncatedCell from "../../../Tooltip/TruncatedCell";
import TruncatedRow from "../../../Tooltip/TrucantedRow";

// Custom debounce hook
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

function Requests({ profileData, loading }) {
  const [searchTerm, setSearchTerm] = useState("");

  // Debounce the search term with 300ms delay
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Default requests data if API data is not available
  const defaultRequestsData = [
    {
      requestId: "17724260",
      type: "Request Access",
      justification: "General Access",
      requestedBy: "AKSHAY SARDA",
      startDate: "04-Dec-2024",
      endDate: "-",
      items: 1,
      createdOn: "04-Dec-2024",
    },
  ];

  // Use API data if available, otherwise use default data
  const requestsData = profileData?.requests || defaultRequestsData;

  const columns = [
    { name: <TruncatedCell text="Request ID"/>, 
        selector: (row) => row.requestId,
        cell:(row) => <TruncatedRow text={row.requestId} />,
             sortable: true },
    { name: "Type", selector: (row) => row.type },
    { name: "Justification", selector: (row) => row.justification },
    { name: <TruncatedCell text="Requested By"/>, selector: (row) => row.requestedBy, cell:(row) => <TruncatedRow text={row.requestedBy} /> },
    { name:  <TruncatedCell text="Start/Active Date"/>, selector: (row) => row.startDate },
    { name:  <TruncatedCell text="End/Removal Date"/>, selector: (row) => row.endDate },
    { name: "Items", selector: (row) => row.items },
    { name: <TruncatedCell text= "Created On"/>, selector: (row) => row.createdOn },
  ];

  const filteredData = useMemo(() => {
    if (!debouncedSearchTerm) return requestsData;
    return requestsData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      )
    );
  }, [requestsData, debouncedSearchTerm]);

  if (loading) {
    return (
      <div className="bg-white rounded-[10px] p-4">
        <h3 className="font-poppins text-[14px] text-[#4F2683] font-[500] mb-4">Requests</h3>
        <div className="flex justify-center items-center py-8">
          <p className="text-[#7C7C7C] font-poppins text-[12px]">Loading requests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Requests"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={columns}
        data={filteredData}
        fixedHeader
        fixedHeaderScrollHeight="400px"
        highlightOnHover
        striped
      />
    </div>
  );
}

export default Requests;