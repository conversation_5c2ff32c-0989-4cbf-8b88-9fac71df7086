import api from "./";

/**
 * Get areas owned by the logged-in user.
 *
 * @returns {Promise<any>} A promise that resolves to the owned areas data.
 */
export const getOwnedAreas = async () => {
  const response = await api.get("/myaccess/owned-areas");
  return response.data;
};

/**
 * Get identities that have access to areas owned by the logged-in user.
 *
 * @returns {Promise<any>} A promise that resolves to the accessible identities data.
 */
export const getAccessibleIdentities = async () => {
  const response = await api.get("/myaccess/accessible-identities");
  return response.data;
};
