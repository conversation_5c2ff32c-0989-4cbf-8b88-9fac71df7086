import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import GenericTable from "../GenericTable";
import Swal from "sweetalert2";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import TruncatedRow from "../Tooltip/TrucantedRow";
import Delete from "../../Images/Delete.svg";
import { FiEdit2 } from "react-icons/fi";
import { MdQrCode2 } from "react-icons/md";
import AddDeviceModal from "./AddDeviceModal";
import EditDeviceModal from "./EditDeviceModal";
import DeviceViewModal from "./DeviceViewModal";
import DeviceQrModal from "./DeviceQrModal";
import { getDevicesForFacility, deleteDevice, updateDevice } from "../../api/Device";
import { useSelector } from "react-redux";

const Device = (props) => {
  // Get current facility from Redux store
  const selectedFacilityId = useSelector(state => state.facility.selectedFacilityId);
  const navigate = useNavigate();

  // State
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("device");
  const [sortOrder, setSortOrder] = useState("ASC");
  const [selectedRow, setSelectedRow] = useState(null);
  const [loading, setLoading] = useState(false);

  // Modal flags
  const [isAddModalOpen, setAddModalOpen] = useState(false);
  const [isEditModalOpen, setEditModalOpen] = useState(false);
  const [isQRModalOpen, setQRModalOpen] = useState(false);
  const [isViewModalOpen, setViewModalOpen] = useState(false);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // New entry form
  const [newEntry, setNewEntry] = useState({
    device: "",
    deviceGroup: "",
    building: "",
    floor: "",
    room: "",
  });

  // Fetch devices from API with server-side search and sorting
  const fetchDevices = useCallback(async () => {
    if (!selectedFacilityId) return;

    try {
      setLoading(true);

      // Build API parameters for server-side search and sorting
      const apiParams = {
        search: debouncedSearchTerm || undefined,
        sortBy: sortBy || undefined,
        sortOrder: sortOrder || undefined,
      };

      const response = await getDevicesForFacility(selectedFacilityId, apiParams);

      // Flatten and map the API response to match table columns
      const devices = (response?.data?.data || []).map(d => {
        console.log("Device API response:", d); // Debug log to see the structure
        return {
          id: d.device_id,
          device_id: d.identifier, // Show device ID from identifier
          device: d.name,
          deviceGroup: d.kiosk_group?.name || "",
          kiosk_group_id: d.kiosk_group_id, // Store the actual kiosk_group_id
          facility_id: d.facility_id, // Store facility_id for API calls
          building: d.building?.name || "",
          floor: d.floor?.floor_number || "",
          room: d.room?.room_number || "",
          qr_code: d.qr_code || d.device_id, // Use QR code from API or fallback to device_id
        };
      });

      setData(devices);
      setFilteredData(devices);
    } catch (error) {
      console.error("Error fetching devices:", error);
      toast.error("Failed to fetch devices.");
    } finally {
      setLoading(false);
    }
  }, [selectedFacilityId, debouncedSearchTerm, sortBy, sortOrder]);

  // Fetch devices when dependencies change
  useEffect(() => {
    fetchDevices();
  }, [fetchDevices]);

  // Server-side search and filtering is now handled in fetchDevices
  // No client-side filtering needed

  // Handlers for server-side search and filtering
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    // Debounce search to avoid too many API calls
  };

  const handleSort = (column, dir) => {
    setSortBy(column.id);
    setSortOrder(dir.toUpperCase());
  };


  const handleView = row => {
    setSelectedRow(row);
    setViewModalOpen(true);
  };
  const handleEdit = row => {
    setSelectedRow(row);
    setEditModalOpen(true);
  };
  const handleDelete = id => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this device?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          await deleteDevice(selectedFacilityId, id);
          setData(prev => prev.filter(item => item.id !== id));
          setFilteredData(prev => prev.filter(item => item.id !== id));
          toast.success("Device deleted successfully!");
        } catch (error) {
          console.error("Error deleting device:", error);
          toast.error("Failed to delete device. Please try again.");
        }
      }
    });
  };
  const handleQR = row => {
    setSelectedRow(row);
    setQRModalOpen(true);
  };
  const handleDeviceGroupClick = row => {
    console.log("Device Group clicked:", row);
    if (row.facility_id && row.kiosk_group_id && row.id) {
      console.log("Switching to Device Group tab with facility_id:", row.facility_id);
      // Use the tab change function passed from parent
      if (props.onTabChange) {
        props.onTabChange('Device Group', { 
          kiosk_group_id: row.kiosk_group_id,
          facility_id: row.facility_id,
          deviceGroup: row.deviceGroup 
        });
      } else {
        // Fallback: dispatch custom event for parent to listen
        const tabChangeEvent = new CustomEvent('switchToDeviceGroup', { 
          detail: { tab: 'Device Group', deviceData: row } 
        });
        window.dispatchEvent(tabChangeEvent);
      }
      toast.success(`Switching to Device Group: ${row.deviceGroup}`);
    } else {
      console.log("No facility_id available in device data:", row);
      toast.error("No facility ID available for this device.");
    }
  };
  const handleAddSave = async () => {
    try {
      // Refresh data from API after successful add
      const refreshedData = await getDevicesForFacility(selectedFacilityId);
      setData(refreshedData);
      setFilteredData(refreshedData);
      setNewEntry({ device: "", deviceGroup: "", building: "", floor: "", room: "" });
      setAddModalOpen(false);
      toast.success("Device added successfully!");
    } catch (error) {
      console.error("Error refreshing devices after add:", error);
      toast.error("Device added but failed to refresh list. Please refresh the page.");
    }
  };
  const handleEditSave = async (updatedDevice) => {
    try {
      // Create API-compatible object with only allowed fields
      const apiData = {
        name: updatedDevice.device,
        kiosk_group_id: updatedDevice.deviceGroup
      };

      // Update the device using the API
      await updateDevice(selectedFacilityId, updatedDevice.device_id, apiData);

      // Update local data without fetching again
      const updatedData = data.map(item =>
        item.id === updatedDevice.id ? updatedDevice : item
      );
      setData(updatedData);
      setFilteredData(updatedData);
      setEditModalOpen(false);
      toast.success("Device updated successfully!");
    } catch (error) {
      console.error("Error updating device:", error);
      toast.error("Failed to update device. Please try again.");
    }
  };

  // Table cols
  const columns = [
    {
      id: "device_id",
      name: "Device ID",
      selector: row => row.device_id,
      sortable: true,
      cell: row => (
        <span
          className="cursor-pointer"
          onClick={() => {
            navigator.clipboard.writeText(row.device_id);
            toast.success("Device ID copied!");
          }}
        >
          <TruncatedRow text={row.device_id} />
        </span>
      ),
    },
    {
      id: "device",
      name: "Device",
      selector: row => row.device,
      sortable: true,
      cell: row => (
        <span
          className="text-[#4F2683] underline cursor-pointer"
          onClick={() => handleView(row)}
        >
          <TruncatedRow text={row.device} />
        </span>
      ),
    },
    {
      id: "deviceGroup",
      name: "Device Group",
      selector: row => row.deviceGroup,
      sortable: true,
      cell: row => (
        <span
          className="text-[#4F2683] underline cursor-pointer"
          onClick={() => handleDeviceGroupClick(row)}
          title="Click to view Device Group"
        >
          <TruncatedRow text={row.deviceGroup || "View Device Group"} />
        </span>
      ),
    },
    {
      id: "building",
      name: "Building",
      selector: row => row.name,
      sortable: true,
      cell: row => <TruncatedRow text={row.name} />,
    },
    {
      id: "floor",
      name: "Floor",
      selector: row => row.floor,
      sortable: true,
      cell: row => <TruncatedRow text={row.floor} />,
    },
    {
      id: "room",
      name: "Room",
      selector: row => row.room,
      sortable: true,
      cell: row => <TruncatedRow text={row.room} />,
    },
    {
      name: "Action",
      button: true,
      ignoreRowClick: true,
      center: true,
      allowOverflow: true,
      cell: row => (
        <div className="flex items-center gap-2">
          <img
            src={Delete}
            alt="Delete"
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer"
            onClick={() => handleDelete(row.id)}
          />
          <FiEdit2
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#4F2683]"
            onClick={() => handleEdit(row)}
          />
          <MdQrCode2
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#4F2683]"
            onClick={() => handleQR(row)}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="relative">
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Devices"
          searchTerm={searchTerm}
          onSearchChange={handleSearch}
          onAdd={() => setAddModalOpen(true)}
          columns={columns}
          data={filteredData}
          onSort={handleSort}
          sortServer
          showSearch
          showAddButton
          loading={loading}

        />
      </div>

      <ToastContainer />

      <AddDeviceModal
        open={isAddModalOpen}
        onClose={() => setAddModalOpen(false)}
        onSave={handleAddSave}
        newEntry={newEntry}
        setNewEntry={setNewEntry}
      />

      <EditDeviceModal
        open={isEditModalOpen}
        onClose={() => setEditModalOpen(false)}
        onSave={handleEditSave}
        selectedRow={selectedRow}
        setSelectedRow={setSelectedRow}
      />

      <DeviceQrModal
        open={isQRModalOpen}
        device={selectedRow}
        onClose={() => setQRModalOpen(false)}
      />

      <DeviceViewModal
        device={isViewModalOpen ? selectedRow : null}
        onClose={() => setViewModalOpen(false)}
      />
    </div>
  );
};

export default Device;
