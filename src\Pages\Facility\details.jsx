import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Facility from "../../Components/Facility/FacilityEdit.jsx";
import Building from "../../Components/Facility/Building/Building.jsx";
import Floor from "../../Components/Facility/Floor/Floor.jsx";
import Room from "../../Components/Facility/Room/Room.jsx";
import AccessAreas from "../../Components/Facility/AccessArea/AccessAreas.jsx";
import Device from "../../Components/Device/Device.jsx";
import DeviceGroup from "../../Components/DeviceGroup/DeviceGroup.jsx";
import DetailsCard from "../../Components/Global/DetailsCard.jsx";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal.jsx";
import Loader from "../../Components/Loader.jsx";
import userImg from "../../Images/Building.svg";
import { IoIosArrowBack } from "react-icons/io";
import HistoryTable from "../../Components/Observation/HistoryTable.jsx";
import { getFacilityById } from "../../api/facility.js";

const WatchListDetails = ({ tab = null }) => {
  const { facilityId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  // Get tab from URL query parameter or use default
  const queryParams = new URLSearchParams(location.search);
  const tabFromUrl = queryParams.get('tab');
  const initialTab = tabFromUrl || tab || t('facility.tab_facility');

  const [selectedTab, setSelectedTab] = useState(initialTab);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [facilityData, setFacilityData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);
  const [deviceGroupData, setDeviceGroupData] = useState(null);
  const deviceGroupDataRef = useRef(null);

  const tabKeys = [
    'facility.tab_facility',
    'facility.tab_building',
    'facility.tab_floor',
    'facility.tab_room',
    'facility.tab_access_areas',
    'Devices',
    'Device Group'
  ];

  const handleHistoryOpen = () => setIsHistoryPanelOpen(true);

  const handleTabChange = (tabName, additionalData = null) => {
    setSelectedTab(tabName);
    if (additionalData && additionalData.kiosk_group_id) { //for device group tab
      // Store the kiosk_group_id for the Device Group component
      // setDeviceGroupData(additionalData);
      deviceGroupDataRef.current = additionalData;
    }
  };

  // Function to handle image capture
  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc);
    setIsModalOpen(false);
  };

  const fetchFacilityData = async () => {
    try {
      const response = await getFacilityById(facilityId);
      if (response && response.status) {
        console.log(response.data);
        setFacilityData(response.data);
      }
    } catch (error) {
      console.error("Error fetching facility data:", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (!facilityId) {
      console.error("Facility ID is missing");
      setLoading(false);
      return;
    }
    fetchFacilityData();
  }, [facilityId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader />
      </div>
    );
  }
  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-20 pt-20">
      {/* Profile Section */}
      <div className="flex items-center text-[#4F2683]">
        <div
          className="flex items-center gap-1 cursor-pointer"
          onClick={() => navigate("/facility")}
        >
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">{t('facility.title')}</h2>
        </div>
      </div>
      <DetailsCard
        OpenPhotoModal={() => setIsModalOpen(true)}
        handleHistoryOpen={handleHistoryOpen}
        profileImage={profileImage}
        defaultImage={userImg}
        name={facilityData?.name || "N/A"}
        showHistoryButton={true}
        additionalFields={[
          {
            label: t('facility.address'),
            value: facilityData?.address?.address_line_1 || "N/A",
          },
          {
            label: "Country",
            value: facilityData?.address?.country_name || "N/A",
          },
          {
            label: "State",
            value: facilityData?.address?.state_name || "N/A",
          },
          {
            label: t('facility.status'),
            value: facilityData?.facility_status_name?.value || "N/A",
          },
          {
            label: t('facility.type'),
            value: facilityData?.facility_type_name?.value || "N/A",
          },
        ]}
      />

      {/* Sidebar and Main Content */}
      <div className="flex">
        <div className="w-[12%] mt-6">
          {tabKeys.map((tabKey) => (
            <button
              key={tabKey}
              className={`block w-full text-left p-2 mb-2 ${selectedTab === t(tabKey) || (tabKey === 'Devices' && selectedTab === 'Devices') || (tabKey === 'Device Group' && selectedTab === 'Device Group') ? 'text-[#4F2683] border-l-2 border-[#4F2683]' : 'text-gray-700'}`}
              onClick={() => setSelectedTab(tabKey === 'Devices' || tabKey === 'Device Group' ? tabKey : t(tabKey))}
            >
              {tabKey === 'Devices' || tabKey === 'Device Group' ? tabKey : t(tabKey)}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div className="w-[88%] pt-4">
          {selectedTab === t('facility.tab_facility') && (
            <Facility
              facility={facilityData}
              refreshFacilityData={fetchFacilityData}
            />
          )}
          {selectedTab === t('facility.tab_building') && <Building facility={facilityData} />}
          {selectedTab === t('facility.tab_floor') && <Floor facility={facilityData} />}
          {selectedTab === t('facility.tab_room') && <Room />}
          {selectedTab === t('facility.tab_access_areas') && <AccessAreas />}
          {selectedTab === 'Devices' && <Device onTabChange={handleTabChange} />}
          {selectedTab === 'Device Group' && <DeviceGroup kioskGroupData={deviceGroupDataRef.current} />}
        </div>
      </div>
      <HistoryTable
        isOpen={isHistoryPanelOpen}
        onClose={() => setIsHistoryPanelOpen(false)}
      />
      {isModalOpen && (
        <EditPhotoModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default WatchListDetails;
