import React, { useState, useEffect, useCallback } from "react";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import GenericTable from "../GenericTable";
import Swal from "sweetalert2";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FiEdit2 } from "react-icons/fi";
import { MdDelete } from "react-icons/md";
import AddDeviceGroupModal from "./AddDeviceGroupModal";
import EditDeviceGroupModal from "./EditDeviceGroupModal";
import ViewDeviceGroupModal from "./ViewDeviceGroupModal";
import {
  getDeviceGroups,
  deleteDeviceGroup,
  updateDeviceGroup,
  createDeviceGroups
} from "../../api/device_group";

const DeviceGroup = ({ kioskGroupData }) => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("");
  const [sortOrder, setSortOrder] = useState("ASC");
  const [isAddModalOpen, setAddModalOpen] = useState(false);
  const [isEditModalOpen, setEditModalOpen] = useState(false);
  const [isViewModalOpen, setViewModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [viewRow, setViewRow] = useState(null);
  const [loading, setLoading] = useState(false);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const { kioskGroupId, facilityId } = useParams();
  const selectedFacilityId = useSelector(state => state.facility.selectedFacilityId);
  const effectiveKioskGroupId = facilityId || kioskGroupId || selectedFacilityId;

  // Fetch device groups with server-side search and sorting
  const fetchDeviceGroups = useCallback(async () => {
    setLoading(true);
    try {
      // Build API parameters for server-side search and sorting
      const apiParams = {
        search: debouncedSearchTerm || undefined,
        sortBy: sortBy || undefined,
        sortOrder: sortOrder || undefined,
      };

      const response = await getDeviceGroups(kioskGroupData?.kiosk_group_id || '', apiParams);

      let deviceGroupsData = [];
      if (response?.data?.data) {
        deviceGroupsData = response.data.data;
      } else if (response?.data) {
        deviceGroupsData = Array.isArray(response.data) ? response.data : [];
      } else if (Array.isArray(response)) {
        deviceGroupsData = response;
      }

      console.log("Raw API data:", deviceGroupsData);

      if (deviceGroupsData.length > 0) {
        const firstItem = deviceGroupsData[0];

        if (firstItem.kiosk_group_setting_id) {
          const transformedData = deviceGroupsData.map((setting) => ({
            id: setting.kiosk_group_setting_id,
            deviceGroup: setting.name || "Device Group",
            assignApp: setting.description || "Device group setting",
            status: setting.status || "Active",
            originalData: setting
          }));
          
          setData(transformedData);
          setFilteredData(transformedData);
          // toast.success("Device groups loaded successfully");
        } else if (firstItem.device_id) {
          const groupedByKioskGroup = deviceGroupsData.reduce((acc, device) => {
            const groupName = device.kiosk_group?.name || 'Unknown Group';
            const groupId = device.kiosk_group_id || 'unknown';

            if (!acc[groupId]) {
              acc[groupId] = {
                groupName,
                devices: [],
                groupId
              };
            }
            acc[groupId].devices.push(device);
            return acc;
          }, {});

          const transformedData = Object.values(groupedByKioskGroup).map((group) => ({
            id: group.groupId,
            deviceGroup: group.groupName,
            assignApp: `${group.devices.length} devices: ${group.devices.map(d => d.name).join(', ')}`,
            status: "Active",
            originalData: group
          }));

          setData(transformedData);
          setFilteredData(transformedData);
          toast.success("Device groups loaded successfully");
        }
      } else {
        setData([]);
        setFilteredData([]);
        toast.info("No device groups found");
      }
    } catch (error) {
      console.error("Error fetching device groups:", error);
      setData([]);
      setFilteredData([]);
      toast.error("Failed to load device groups");
    } finally {
      setLoading(false);
    }
  }, [kioskGroupData?.kiosk_group_id, debouncedSearchTerm, sortBy, sortOrder]);

  // Fetch device groups when dependencies change
  useEffect(() => {
    fetchDeviceGroups();
  }, [fetchDeviceGroups]);

  // Handlers for server-side search and filtering
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    // Server-side search will be triggered by useEffect
  };

  const handleSort = (column, direction) => {
    setSortBy(column.id);
    setSortOrder(direction.toUpperCase());
  };



  // Server-side search and filtering is now handled in fetchDeviceGroups
  // No client-side filtering needed

  const handleAdd = () => setAddModalOpen(true);
  const handleEdit = row => {
    setSelectedRow(row);
    setEditModalOpen(true);
  };
  const handleView = row => {
    setViewRow(row);
    setViewModalOpen(true);
  };

  const handleDelete = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this device group?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    });

    if (result.isConfirmed) {
      try {
        const kioskGroupIdToUse = kioskGroupData?.kiosk_group_id || effectiveKioskGroupId;
        
        if (!kioskGroupIdToUse) {
          toast.error("No kiosk group ID available for deleting device group");
          return;
        }

        console.log("Deleting with kiosk_group_id:", kioskGroupIdToUse, "and setting_id:", id);
        
        await deleteDeviceGroup(kioskGroupIdToUse, id);
        await fetchDeviceGroups();
        toast.success("Device group deleted successfully!");
      } catch (error) {
        console.error("Delete error:", error);
        if (error.response?.data?.message?.includes("kiosk_group_id does not exist")) {
          toast.error("Invalid kiosk group ID. Please refresh and try again.");
        } else {
          toast.error("Failed to delete device group");
        }
      }
    }
  };

  const handleAddSave = async (newDeviceGroup) => {
    try {
      const kioskGroupIdToUse = kioskGroupData?.kiosk_group_id;
      if (!kioskGroupIdToUse) {
        toast.error("No kiosk group ID available");
        return;
      }

      // Build settings array from assignApp
      let settings = [];
      if (Array.isArray(newDeviceGroup.assignApp) && newDeviceGroup.assignApp.length > 0) {
        settings = newDeviceGroup.assignApp.map(app => {
          if (typeof app === 'object') {
            return {
              kiosk_setting_id: app.id || app.kiosk_setting_id,
              config_value: app.config_value || "true"
            };
          } else {
            return {
              kiosk_setting_id: app,
              config_value: "true"
            };
          }
        });
      }

      if (settings.length === 0) {
        toast.error("Please select at least one valid app to assign");
        return;
      }

      await createDeviceGroups(kioskGroupIdToUse, { settings });
      await fetchDeviceGroups();
      setAddModalOpen(false);
      toast.success("Device group added successfully!");
    } catch (error) {
      console.error("Add error:", error);
      if (error.response?.data?.message?.includes("kiosk_group_id does not exist")) {
        toast.error("Invalid kiosk group ID. Please refresh and try again.");
      } else {
        toast.error("Failed to create device group");
      }
    }
  };

  const handleEditSave = async (updatedRow) => {
    try {
      const kioskGroupIdToUse = kioskGroupData?.kiosk_group_id;
      if (!kioskGroupIdToUse) {
        toast.error("No kiosk group ID available");
        return;
      }

      // For now, always set config_value to "true". Change as needed for dynamic value.
      await updateDeviceGroup(kioskGroupIdToUse, updatedRow.id, { config_value: "true" });
      await fetchDeviceGroups();
      setEditModalOpen(false);
      toast.success("Device group updated successfully!");
    } catch (error) {
      console.error("Edit error:", error);
      if (error.response?.data?.message?.includes("kiosk_group_id does not exist")) {
        toast.error("Invalid kiosk group ID. Please refresh and try again.");
      } else {
        toast.error("Failed to update device group");
      }
    }
  };

  const columns = [
    {
      id: "deviceGroup",
      name: "Device Group",
      selector: row => row.deviceGroup,
      sortable: true,
      cell: row => (
        <span
          className="text-[#4F2683] underline cursor-pointer"
          onClick={() => handleView(row)}
        >
          {row.deviceGroup}
        </span>
      ),
    },
    {
      id: "assignApp",
      name: "Assign App",
      selector: row => row.assignApp,
      sortable: true,
    },
    {
      id: "status",
      name: "Status",
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <span
          className={`px-3 py-1 rounded-full text-xs font-semibold ${
            row.status === "Active"
              ? "bg-[#F4F2F7] text-[#4F2683]"
              : "bg-gray-200 text-gray-500"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      button: true,
      ignoreRowClick: true,
      center: true,
      allowOverflow: true,
      cell: row => (
        <div className="flex items-center gap-2">
          <FiEdit2
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#4F2683]"
            title="Edit"
            onClick={() => handleEdit(row)}
          />
          <MdDelete
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#E74C3C]"
            title="Delete"
            onClick={() => handleDelete(row.id)}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="relative">
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Device Group"
          searchTerm={searchTerm}
          onSearchChange={handleSearch}
          onAdd={handleAdd}
          onSort={handleSort}
          columns={columns}
          data={filteredData}
          showSearch
          showAddButton
          loading={loading}

        />
      </div>

      <ToastContainer />

      <AddDeviceGroupModal
        isOpen={isAddModalOpen}
        onClose={() => setAddModalOpen(false)}
        onSave={handleAddSave}
        facilityId={kioskGroupData?.kiosk_group_id}
      />

      <EditDeviceGroupModal
        isOpen={isEditModalOpen}
        selectedRow={selectedRow}
        onClose={() => setEditModalOpen(false)}
        onSave={handleEditSave}
        facilityId={kioskGroupData?.kiosk_group_id}
      />

      <ViewDeviceGroupModal
        isOpen={isViewModalOpen}
        row={viewRow}
        onClose={() => setViewModalOpen(false)}
      />
    </div>
  );
};

export default DeviceGroup;
