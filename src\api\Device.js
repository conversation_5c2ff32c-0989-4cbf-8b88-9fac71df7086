import api from "./";

/**
 * Get all devices for a facility with search and sorting support.
 * @param {string} facilityId - The ID of the facility.
 * @param {object} params - Query parameters for search and sorting.
 * @param {string} [params.search] - Search term for filtering devices.
 * @param {string} [params.sortBy] - Field to sort by.
 * @param {string} [params.sortOrder] - Sort order (ASC or DESC).
 * @returns {Promise<any>} A promise that resolves to the list of devices.
 */
export const getDevicesForFacility = async (facilityId, params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  if (params.sortBy) query.sortBy = params.sortBy;
  if (params.sortOrder) query.sortOrder = params.sortOrder;

  const response = await api.get(`/facility/devices/${facilityId}`, { params: query });
  return response.data;
};

/**
 * Create a new device in a facility.
 * @param {string} facilityId - The ID of the facility.
 * @param {object} deviceData - The data for the new device.
 * @returns {Promise<any>} A promise that resolves to the created device data.
 */
export const createDeviceInFacility = async (facilityId, deviceData) => {
  const response = await api.post(`/facility/devices/${facilityId}`, deviceData);
  return response.data;
};

/**
 * Get a specific device by ID in a facility.
 * @param {string} facilityId - The ID of the facility.
 * @param {string} deviceId - The ID of the device.
 * @returns {Promise<any>} A promise that resolves to the device data.
 */
export const getDeviceById = async (facilityId, deviceId) => {
  const response = await api.get(`/facility/devices/${facilityId}/${deviceId}`);
  return response.data;
};

/**
 * Update a device in a facility.
 * @param {string} facilityId - The ID of the facility.
 * @param {string} deviceId - The ID of the device.
 * @param {object} updateData - The data to update the device with.
 * @returns {Promise<any>} A promise that resolves to the updated device data.
 */
export const updateDevice = async (facilityId, deviceId, updateData) => {
  const response = await api.patch(`/facility/devices/${facilityId}/${deviceId}`, updateData);
  return response.data;
};

/**
 * Delete a device from a facility.
 * @param {string} facilityId - The ID of the facility.
 * @param {string} deviceId - The ID of the device.
 * @returns {Promise<any>} A promise that resolves to the deletion confirmation.
 */
export const deleteDevice = async (facilityId, deviceId) => {
  const response = await api.delete(`/facility/devices/${facilityId}/${deviceId}`);
  return response.data;
};
