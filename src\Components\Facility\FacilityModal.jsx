import React, { useEffect, useState } from "react";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Input from "../Global/Input/Input";
import Button from "../Global/Button";
import ImageCapture from "../Global/ImageAndCamera/ImageCaptureForForm";
import CustomDropdown from "../Global/CustomDropdown";
import { toast } from "react-toastify";
import { useFacilityMasterData } from "../../hooks/useFacilityMasterData";
import { useCountryMasterData } from "../../hooks/useCountryMasterData";
import { useStateMasterData } from "../../hooks/useStateMasterData";
import { useTimezoneMasterData } from "../../hooks/useTimezoneMasterData";
import { sanitizeRequest } from "../../utils/helpers";
import { createFacility } from "../../api/facility";

const FacilityModal = ({ onClose, fetchFacilities }) => {
  const [selectedCountry, setSelectedCountry] = useState(null);
  const states = useStateMasterData(selectedCountry);

  const { masterData = { facility_status: [], facility_type: [] } } = useFacilityMasterData();
  const { countries: countryData } = useCountryMasterData();
  const countries = Array.isArray(countryData) ? countryData : [];
  const { timezones: timezoneData } = useTimezoneMasterData();
  const timezones = Array.isArray(timezoneData) ? timezoneData : [];

  const facilitySchema = yup.object().shape({
    name: yup.string().required("Facility Name is required"),
    status: yup.number().required("Facility status is required"),
    facility_url: yup.string().url("Enter a valid URL").nullable().notRequired(),
    facility_notes: yup.string().max(500, "Notes must be under 500 characters").nullable().notRequired(),
    geo_location_code: yup.string().matches(/^[A-Za-z0-9\-_,. ]*$/, "Invalid input format - geo_location_code").nullable().notRequired(),
    facility_email: yup.string().email("Enter a valid email").nullable().notRequired(),
   
    address: yup.object().shape({
      address_line_1: yup.string().required("Address Line 1 is required"),
    }),
  });

  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    setValue,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(facilitySchema),
  });

  useEffect(() => {
    if (countries.length > 0 && !selectedCountry) {
      const defaultCountry = countries.find(c => c.name === "India");
      if (defaultCountry) {
        setSelectedCountry(defaultCountry.country_id);
        setValue("address.country_id", defaultCountry.country_id);
      }
    }
  }, [countries]);

  useEffect(() => {
    if (masterData.facility_status?.length > 0) {
      const activeStatus = masterData.facility_status.find(status => 
        status.value.toLowerCase() === "active"
      );
      if (activeStatus) {
        setValue("status", activeStatus.key);
      }

    }
  }, [masterData.facility_status]);
  

  const onSubmit = async (data) => {
    setLoading(true);
    // Remove timezone_id and state_province
    let { time_zone, ...restData } = data;
    let { state_province, ...restAddress } = restData.address;

    // Collect all new facility fields
    const {
      facility_code,
      facility_url,
      connected_applications,
      facility_notes,
      other_code,
      geo_location_code,
      facility_email,
      facility_phone,
      ...otherFacilityFields
    } = restData;

    let sanitizedData = sanitizeRequest({
      ...otherFacilityFields,
      facility_code,
      facility_url,
      connected_applications,
      facility_notes,
      other_code,
      geo_location_code,
      facility_email,
      facility_phone,
      address: {
        ...restAddress,
        country_id: restAddress.country_id,
        state_id: restAddress.state_id,
      },
    });

    try {
      const response = await createFacility(sanitizedData);
      fetchFacilities();
      toast.success(response.message || "Facility saved successfully!");
      onClose();
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Error adding facility!"
      );
      const errorsData = error.response?.data?.data || error.response?.data || {};
      Object.keys(errorsData).forEach((field) => {
        if (
          ["address_line_1", "address_line_2", "postal_code", "map_url", "region"].includes(field)
        ) {
          setError(`address.${field}`, {
            type: "server",
            message: errorsData[field],
          });
        } else {
          setError(field, { type: "server", message: errorsData[field] });
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const handleImageCaptured = (imageSrc) => setValue("image", imageSrc);
  const handleImageUploaded = (uploadedImage) => setValue("image", uploadedImage);

  const facilityFields = [
    {
      label: "Facility Name *",
      type: "text",
      placeholder: "Facility Name",
      name: "name",
    },
    {
      label: "Status *",
      type: "customDropdown",
      placeholder: "Select status",
      name: "status",
      options: Array.isArray(masterData.facility_status) ? masterData.facility_status.map((item) => ({
        label: item.value,
        value: item.key,
      })) : [],
    },
    {
      label: "Facility Code",
      type: "text",
      placeholder: "Facility Code",
      name: "facility_code",
    },
    {
      label: "Facility URL",
      type: "text",
      placeholder: "Facility URL",
      name: "facility_url",
    },
    {
      label: "Connected Applications",
      type: "text",
      placeholder: "Connected Applications",
      name: "connected_applications",
    },
    {
      label: "Facility Notes",
      type: "text",
      placeholder: "Facility Notes",
      name: "facility_notes",
    },
    {
      label: "Other Code",
      type: "text",
      placeholder: "Other Code",
      name: "other_code",
    },
    {
      label: "Geo Location Code",
      type: "text",
      placeholder: "Geo Location Code",
      name: "geo_location_code",
    },
    {
      label: "Facility Email",
      type: "text",
      placeholder: "Facility Email",
      name: "facility_email",
    },
    {
      label: "Facility Phone",
      type: "text",
      placeholder: "Facility Phone",
      name: "facility_phone",
    },
    {
      label: "Facility Type",
      type: "customDropdown",
      placeholder: "Facility Type",
      name: "facility_type",
      options: masterData.facility_type.map((item) => ({
        label: item.value,
        value: item.key,
      })),
    },
    {
      label: "Time Zone",
      type: "customDropdown",
      placeholder: "Select Time Zone",
      name: "timezone_id",
      options: Array.isArray(timezones) ? timezones.map((item) => ({
        label: item.code,
        value: item.timezone_id,
      })) : [],
    },
  ];

  const addressFields = [
    {
      label: "Address Line 1 *",
      type: "text",
      placeholder: "Address Line 1",
      name: "address_line_1",
    },
    {
      label: "Country",
      type: "customDropdown",
      placeholder: "Select Country",
      name: "country_id", // Change to country_id
      options: Array.isArray(countries) ? countries.map((item) => ({
        label: item.name,
        value: item.country_id, // Use country_id here
      })) : [],
    },
    {
      label: "State / Province",
      type: "customDropdown",
      placeholder: "Select State",
      name: "state_id", // Change to state_id
      options: Array.isArray(states) ? states.map((item) => ({
        label: item.name,
        value: item.state_id, // Use state_id
      })) : [],
    },
    {
      label: "Postal Code",
      type: "number",
      placeholder: "Postal Code",
      name: "postal_code",
    },
  ];

  // Animation state
  const [show, setShow] = useState(false);

  // Mount/unmount logic for smooth open/close
  useEffect(() => {
    // Start hidden, then show after a tick for animation
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Facility</h2>

           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        <div className="mb-16 pt-8">
          <ImageCapture
            onImageCaptured={handleImageCaptured}
            onImageUploaded={handleImageUploaded}
            error={errors["image"]}
          />
        </div>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="p-6">
            <h3 className="font-medium text-[20px] pb-4 text-[#333333]">Facility Details</h3>
            {facilityFields.map(({ label, type, name, options, placeholder }, idx) => {
              return (
                <div key={idx} className="flex mb-2">
                  <label className="mr-2 w-1/3">{label}</label>
                  {type === "customDropdown" ? (
                    <Controller
                      control={control}
                      name={name}
                      render={({ field }) => (
                        <CustomDropdown
                          value={field.value}
                          options={options}
                          placeholder={placeholder}
                          onSelect={field.onChange}
                          bgColor="bg-[white] text-black"
                          textColor="text-black"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="p-2 border h-11 rounded focus:outline-none focus:ring-1"
                          rounded="rounded"
                          error={errors[name]}
                        />
                      )}
                    />
                  ) : (
                    <Input
                      type={type}
                      name={name}
                      placeholder={placeholder}
                      error={errors[name]}
                      {...register(name)}
                    />
                  )}
                </div>
              );
            })}
            <h3 className="font-medium text-[20px] py-4 text-[#333333]">Address Information</h3>
            {addressFields.map(({ label, type, name, placeholder, options }, idx) => (
              <div key={idx} className="flex mb-2">
                <label className="mr-2 w-1/3">{label}</label>
                {type === "customDropdown" ? (
                  <Controller
                    control={control}
                    name={`address.${name}`}
                    render={({ field }) => (
                      <CustomDropdown
                        value={field.value}
                        options={options}
                        placeholder={placeholder}
                        onSelect={(value) => {
                          field.onChange(value);
                          if (name === "country_id") {
                            setSelectedCountry(value); // Update the selected country
                            setValue("address.state_id", ""); // Reset state when country changes
                          }
                        }}
                        bgColor="bg-[white] text-black"
                        textColor="text-black"
                        hoverBgColor="hover:bg-[#4F2683]"
                        borderColor="border-gray-300"
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1"
                        rounded="rounded"
                        error={errors?.address?.[name]}
                      />
                    )}
                  />
                ) : (
                  <Input
                    type={type}
                    name={`address.${name}`}
                    placeholder={placeholder}
                    error={errors?.address?.[name]}
                    {...register(`address.${name}`)}
                  />
                )}
              </div>
            ))}
            <div className="flex justify-center gap-4 mt-6">
              <Button type="cancel" label="Cancel" onClick={onClose} />
              <Button type="primary" label={loading ? "Saving..." : "Add"} disabled={loading} />
            </div>
          </div>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};

export default FacilityModal;