import React, { useState, useEffect, useMemo } from "react";
import EditableSection from "../Global/EditableSection.jsx";
import { updateFacility, updateFacilityAddress } from "../../api/facility.js";
import Loader from "../Loader.jsx";
import { useFacilityMasterData } from "../../hooks/useFacilityMasterData";
import { useTimezoneMasterData } from "../../hooks/useTimezoneMasterData";
import { useSystemMasterData } from "../../hooks/useSystemMasterData";
import { useCountryMasterData } from "../../hooks/useCountryMasterData";
import { useStateMasterData } from "../../hooks/useStateMasterData";
import { toast } from "react-toastify";

const Facilitery = ({ facility, refreshFacilityData }) => {
  const { facility_id } = facility; // Get dynamic facility ID from URL
  const [selectedCountry, setSelectedCountry] = useState(null);
  const states = useStateMasterData(selectedCountry);

  // Initialize selectedCountry when facility data is available
  useEffect(() => {
    if (facility?.address?.country_id) {
      setSelectedCountry(facility.address.country_id);
    }
  }, [facility]);

  // State for facility data
  const [biographicData, setBiographicData] = useState({
    FacilityName: facility.name || "",
    status: facility.facility_status_name || "",
    facilityType: facility.facility_type_name || "",
    TimeZone: facility.time_zone || "",
    ConnectedApplications: facility.connected_applications || "",
    // Optional fields (not required):
    facilityCode: facility.facility_code || "",
    facilityPhone: facility.phone || "",
    facilityEmail: facility.email || "",
    GeoLocationCode: facility.geo_location_code || "",
    OtherCode: facility.other_code || "",
    facilityUrl: facility.facility_url || "",
    facilityNotes: facility.notes || "",
  });
  const [addressData, setAddressData] = useState({
    Address1: facility.address?.address_line_1 || "",
    Address2: facility.address?.address_line_2 || "",
    Country: facility.address?.country_id || "",
    StateProvince: facility.address?.state_id || "",
    PostalCode: facility.address?.postal_code || "",
    MapUrl: facility.address?.map_url || "",
    Region: facility.address?.region || "",
  });

  const handleInputChange = (section, key, value) => {
    if (section === "biographic") {
      setBiographicData((prev) => ({ ...prev, [key]: value }));
    } else if (section === "address") {
      setAddressData((prev) => ({ ...prev, [key]: value }));
      if (key === "Country") {
        setSelectedCountry(value);
        setAddressData((prev) => ({ ...prev, StateProvince: "" })); // Reset state when country changes
      }
    }
  };

  // Handler to save biographic section using updateFacility API
  const handleBiographicSave = async (updatedLocalData) => {
    // Only FacilityName, status, and Address1 are mandatory
    if (!updatedLocalData.FacilityName) {
      toast.error("Facility Name is required");
      return;
    }
    if (!updatedLocalData.status) {
      toast.error("Status is required");
      return;
    }
    const updatedData = {
      name: updatedLocalData.FacilityName,
      status: updatedLocalData.status.key || updatedLocalData.status,
      facility_type: updatedLocalData.facilityType.key || updatedLocalData.facilityType,
      time_zone: updatedLocalData.TimeZone?.timezone_id || updatedLocalData.TimeZone,
      connected_applications: updatedLocalData.ConnectedApplications?.value || updatedLocalData.ConnectedApplications,
      // Optional fields
      facility_code: updatedLocalData.facilityCode,
      phone: updatedLocalData.facilityPhone,
      email: updatedLocalData.facilityEmail,
      geo_location_code: updatedLocalData.GeoLocationCode,
      other_code: updatedLocalData.OtherCode,
      facility_url: updatedLocalData.facilityUrl,
      notes: updatedLocalData.facilityNotes,
    };
    try {
      await updateFacility(facility_id, updatedData);
      toast.success("Facility data updated successfully");
      refreshFacilityData();
    } catch (error) {
      toast.error(error.response && error.response.data
        ? error.response.data.message
        : "Error updating biographic data");
    }
  };

  // Handler to save address section using updateFacility API
  const handleAddressSave = async (updatedLocalData) => {
    const updatedData = {
        address_line_1: updatedLocalData.Address1,
        address_line_2: updatedLocalData.Address2,
        country_id: updatedLocalData.Country,
        state_id: updatedLocalData.StateProvince,
        postal_code: updatedLocalData.PostalCode,
        map_url: updatedLocalData.MapUrl,
        region: updatedLocalData.Region,
    };

    try {
      await updateFacilityAddress(facility_id, updatedData);
      toast.success("Facility address updated successfully");
      refreshFacilityData();
    } catch (error) {
      toast.error(error.response && error.response.data
        ? error.response.data.message
        : "Error updating address data");
    }
  };
  const { facilityStatusOptions, facilityTypeOptions } = useFacilityMasterData();
  const { timezones } = useTimezoneMasterData();
  const { systems } = useSystemMasterData();
  const { countries: countryData } = useCountryMasterData();
  const countries = Array.isArray(countryData) ? countryData : [];

  if (!useFacilityMasterData) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader />
      </div>
    );
  }


  return (
    <div className="bg-gray-100 p-0 ">
      <EditableSection
        title="Biographic"
        data={biographicData}
        onChange={(key, value) => handleInputChange("biographic", key, value)}
        onSave={handleBiographicSave}
        dropdownKeys={["status", "facilityType", "TimeZone", "ConnectedApplications"]}
        dropdownOptions={{
          status: facilityStatusOptions,
          facilityType: facilityTypeOptions,
          TimeZone: timezones.map((item) => ({ label: item.code, value: item.timezone_id, timezone_id: item.timezone_id })),
          ConnectedApplications: systems.map((item) => ({ label: item.name || item.label || item.value, value: item.key || item.value })),
        }}
      />

      <EditableSection
        title="Address"
        data={addressData}
        onChange={(key, value) => handleInputChange("address", key, value)}
        onSave={handleAddressSave}
        dropdownKeys={["Country", "StateProvince"]}
        searchableKeys={["Country", "StateProvince"]}
        // You may want to update these to use master data as well
        dropdownOptions={{
          Country: countries.map((item) => ({ label: item.name, value: item.country_id })),
          StateProvince: Array.isArray(states) ? states.map((item) => ({ label: item.name, value: item.state_id })) : [],
        }}
      />
    </div>
  );
};

export default Facilitery;
