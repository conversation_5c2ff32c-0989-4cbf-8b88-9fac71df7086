import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import FilterPanel from "../../Components/Observation/FilterPanel";
import {
  getWatchlists,
} from "../../api/watchList";
import newWindow from "../../Images/new-window.svg";
import { getMediaByModel } from '../../api/global';
import { useTranslation } from 'react-i18next';
import demo from "../../Images/demoimg.svg"; // Placeholder image
import { useDebounce } from '../../hooks/useDebounce';
const Observation = () => {
  const { t } = useTranslation();
  const [tableData, setTableData] = useState([]);
  const [filter, setFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);

  // Debounce the search term with 300ms delay
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [hasAddedNewEntry, setHasAddedNewEntry] = useState(false);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Helper to normalize status to string
  const mapStatus = (status) => {
    if (typeof status === 'string') return status;
    return status === 1 ? 'Active' : 'Inactive';
  };

  // Fetch watchlist data with images
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const params = {
          search: searchTerm,
          sortBy: '', 
          sortOrder: 'ASC',
        };
        const resp = await getWatchlists(params);
        const items = resp.data?.data || [];

        const formatted = await Promise.all(items.map(async (item) => {
          let imageUrl = demo;
          
          if (item.image_id || item.image) {
            try {
              const media = await getMediaByModel("Watchlist", {
                key: "image",
                value: item.image_id || item.image,
              });
              imageUrl = media.value || demo;
            } catch (err) {
              console.error("Error fetching image:", err);
            }
          }

          return {
            id: item.watchlist_id,
            name: [item.first_name, item.middle_name, item.last_name]
              .filter(Boolean)
              .join(" "),
            addedBy: item.email,
            addedOn: item.createdAt ? new Date(item.createdAt).toLocaleDateString() : "—",
            expirationDate: item.expiry_date ? new Date(item.expiry_date).toLocaleDateString() : "—",
            status: mapStatus(item.status),
            image: imageUrl,
          };
        }));

        setTableData(formatted);
      } catch (err) {
        console.error("Error fetching watchlists:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [searchTerm, filter]);

  // Handle sorting with image fetching
  const handleSort = async (column, direction) => {
    const sortBy = column.id;
    const sortOrder = direction.toUpperCase();
    setLoading(true);

    try {
      const resp = await getWatchlists({ search: searchTerm, sortBy, sortOrder });
      const items = resp.data?.data || [];
      
      const formatted = await Promise.all(items.map(async (item) => {
        let imageUrl = demo;
        if (item.image_id || item.image) {
          try {
            const media = await getMediaByModel("Watchlist", {
              key: "image",
              value: item.image_id || item.image,
            });
            imageUrl = media.value || demo;
          } catch (err) {
            console.error("Error fetching image:", err);
          }
        }

        return {
          id: item.watchlist_id,
          name: [item.first_name, item.middle_name, item.last_name]
            .filter(Boolean)
            .join(" "),
          addedBy: item.email,
          addedOn: item.createdAt ? new Date(item.createdAt).toLocaleDateString() : "—",
          expirationDate: item.expiry_date ? new Date(item.expiry_date).toLocaleDateString() : "—",
          status: mapStatus(item.status),
          image: imageUrl,
        };
      }));

      setTableData(formatted);
    } catch (err) {
      console.error("Error fetching sorted watchlists:", err);
    } finally {
      setLoading(false);
    }
  };

  // Handle new entry from AddForm
  useEffect(() => {
    const newEntryPayload = location.state?.newWatchlistEntry;
    if (newEntryPayload && !hasAddedNewEntry) {
      const newEntry = {
        id: newEntryPayload.id || Date.now(),
        name: `${newEntryPayload.firstName} ${newEntryPayload.lastName}`,
        addedBy: newEntryPayload.email,
        addedOn: new Date().toLocaleDateString(),
        expirationDate: newEntryPayload.expiry_date ? new Date(newEntryPayload.expiry_date).toLocaleDateString() : "—",
        status: mapStatus(newEntryPayload.status),
        image: newEntryPayload.image || demo,
      };

      setTableData((prev) =>
        prev.some((item) => item.id === newEntry.id) ? prev : [newEntry, ...prev]
      );
      setHasAddedNewEntry(true);
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location.state, hasAddedNewEntry, navigate, location.pathname]);

  // Filter data with debounced search term
  const filteredData = tableData
    .filter(row => (filter === 'All' ? true : row.status === 'Active'))
    .filter(row =>
      (row.name?.toLowerCase() || '').includes(debouncedSearchTerm.toLowerCase()) ||
      (row.addedBy?.toLowerCase() || '').includes(debouncedSearchTerm.toLowerCase())
    );

  // const handleFilterOpen = () => setIsFilterPanelOpen(true);
  const handleAddWatchlist = () => navigate('/add-observation-form');

  const columns = [
    {
      id: "first_name",
      name: t('observation.name'),
      selector: row => row.name,
      sortable: true,
      cell: (row, index) => (
        <div
          className="relative flex items-center space-x-3 cursor-pointer group w-full"
          onMouseEnter={() => setHoveredRow(index)}
          onMouseLeave={() => setHoveredRow(null)}
          onClick={() => window.open(`/details?id=${row.id}`, "_blank")}
        >
          <img 
            src={row.image} 
            alt={row.name} 
            className="w-8 h-8 rounded-full object-cover"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = demo;
            }}
          />
          <span>{row.name}</span>
          {hoveredRow === index && (
            <img className="w-4 h-4" src={newWindow} alt={t('observation.open')} />
          )}
        </div>
      ),
    },
    {
      id: "created_by",
      name: t('observation.added_by'),
      selector: row => row.addedBy,
      sortable: true,
    },
    { name: t('observation.added_on'), selector: row => row.addedOn },
    { name: t('observation.expiration_date'), selector: row => row.expirationDate },
    {
      name: t('observation.status'),
      selector: row => row.status,
      cell: row => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full ${
            row.status === 'Active'
              ? 'bg-[#4F268314] bg-opacity-8 text-[#4F2683]'
              : 'bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]'
          }`}
        >
          {t(`observation.status_${row.status.toLowerCase()}`)}
        </span>
      ),
    },
  ];

  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">
          {t('observation.roster_title')}
        </h2>
      </div>
      <div className="mb-4">
        <FilterButtons
          filter={filter}
          onFilterChange={setFilter}
          filterOptions={[
            { label: t('observation.filter_all'), value: 'All' },
            { label: t('observation.filter_active'), value: 'Active' },
          ]}
        />
      </div>
      <GenericTable
        title={t('observation.roster_title')}
        loading={loading}
        searchTerm={searchTerm}
        onSearchChange={e => setSearchTerm(e.target.value)}
        onSort={handleSort}
        columns={columns}
        data={filteredData}
        onAdd={handleAddWatchlist}
        sortServer
        fixedHeader
        fixedHeaderScrollHeight="440px"
      />
      <FilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
      />
    </div>
  );
};

export default Observation;