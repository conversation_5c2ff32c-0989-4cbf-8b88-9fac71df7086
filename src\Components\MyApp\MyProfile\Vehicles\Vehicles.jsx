import React, { useState, useMemo, useEffect } from "react";
import GenericTable from "../../../GenericTable";
import AddVehicleModal from "./AddVehicleModal"; // Import the new modal component

// Custom debounce hook
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

const Vehicles = ({ vehiclesData: apiVehiclesData, loading }) => {
  const [searchTerm, setSearchTerm] = useState("");

  // Debounce the search term with 300ms delay
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Default vehicles data if API data is not available
  const defaultVehicleData = [
    {
      plate: "RJ 22 MI 2345",
      year: "2025",
      make: "2000",
      model: "Hero",
      color: "Black",
      updatedDate: "19-Mar-2025",
    },
  ];

  const [vehicleData, setVehicleData] = useState(apiVehiclesData || defaultVehicleData);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Update vehicles data when API data changes
  React.useEffect(() => {
    if (apiVehiclesData) {
      setVehicleData(apiVehiclesData);
    }
  }, [apiVehiclesData]);

  const vehicleColumns = [
    { name: "Plate", selector: (row) => row.plate, sortable: true },
    { name: "Year", selector: (row) => row.year },
    { name: "Make", selector: (row) => row.make },
    { name: "Model", selector: (row) => row.model },
    { name: "Color", selector: (row) => row.color },
    { name: "Updated Date", selector: (row) => row.updatedDate },
  ];

  const filteredVehicleData = useMemo(() => {
    if (!debouncedSearchTerm) return vehicleData;
    return vehicleData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      )
    );
  }, [vehicleData, debouncedSearchTerm]);

  const handleAddVehicle = (newVehicle) => {
    setVehicleData((prevData) => [newVehicle, ...prevData]); // Add new vehicle at the top
    setIsModalOpen(false);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-[10px] p-4">
        <h3 className="font-poppins text-[14px] text-[#4F2683] font-[500] mb-4">My Vehicles</h3>
        <div className="flex justify-center items-center py-8">
          <p className="text-[#7C7C7C] font-poppins text-[12px]">Loading vehicles...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        onAdd={() => setIsModalOpen(true)} // Open modal on add button click
        title={"My Vehicles"}
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={vehicleColumns}
        data={filteredVehicleData}
        fixedHeader
        fixedHeaderScrollHeight="400px"
        highlightOnHover
        striped
      />
      {isModalOpen && (
        <AddVehicleModal
          isOpen={isModalOpen} // Pass isOpen prop
          onClose={() => setIsModalOpen(false)}
          onSave={handleAddVehicle}
        />
      )}
    </div>
  );
};

export default Vehicles;
