import React from 'react';

const Identity = ({ profileData, loading, error }) => {
  // Default profile details if API data is not available
  const defaultProfileDetails = {
    FirstName: 'Adam',
    MiddleName: 'LETHLEAN',
    LastName: 'LETHLEAN',
    PreferredFirstName: 'ADAM LETHLEAN',
    EID: '1234',
    PrimaryWorkEmail: '<EMAIL>',
    WorkPhone: '123 - 123456',
    MobilePhone: '1234567890',
  };

  // Use API data if available, otherwise use default data
  const profileDetails = profileData || defaultProfileDetails;

  // Helper function to format key labels (e.g., facilityName -> Facility Name)
  const formatKey = (key) => {
    const withSpaces = key.replace(/([A-Z])/g, ' $1');
    return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1).trim();
  };

  if (loading) {
    return (
      <div className="">
        <div className="bg-white rounded-[10px] p-4 mb-2">
          <h3 className="font-[500] text-[14px] text-[#4F2683] font-poppins">Profile Details</h3>
          <hr className="my-2" />
          <div className="flex justify-center items-center py-8">
            <p className="text-[#7C7C7C] font-poppins text-[12px]">Loading profile details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="">
        <div className="bg-white rounded-[10px] p-4 mb-2">
          <h3 className="font-[500] text-[14px] text-[#4F2683] font-poppins">Profile Details</h3>
          <hr className="my-2" />
          <div className="flex justify-center items-center py-8">
            <p className="text-red-500 font-poppins text-[12px]">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      <div className="bg-white rounded-[10px] p-4 mb-2">
        <h3 className="font-[500] text-[14px] text-[#4F2683] font-poppins">Profile Details</h3>
        <hr className="my-2" />
        <div>
          {Object.entries(profileDetails).map(([key, value]) => (
            <div className="flex items-start mb-2" key={key}>
              <div className="w-1/4">
                <p className="text-[#7C7C7C] font-poppins text-[12px]">{formatKey(key)}</p>
              </div>
              <div className="w-3/4">
                <p className="text-[#000] font-[400] font-poppins text-[12px]">
                  {value && (typeof value === 'object' ? (value.value || value.label) : value)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Identity;
