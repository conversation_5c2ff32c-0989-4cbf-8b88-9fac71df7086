import React, { useEffect, useState } from "react";
import { getKioskSettings, getKioskGroups } from "../../api/global";
import CustomDropdown from "../Global/CustomDropdown";

const EditDeviceGroupModal = ({ isOpen, selectedRow, onClose, onSave, facilityId }) => {
  const [editEntry, setEditEntry] = useState(selectedRow);
  const [show, setShow] = useState(false);
  const [kioskApps, setKioskApps] = useState([]);
  const [deviceGroups, setDeviceGroups] = useState([]);

  useEffect(() => {
    if (selectedRow) {
      // Convert assignApp string back to array if needed
      const assignAppArray = typeof selectedRow.assignApp === 'string'
        ? selectedRow.assignApp.split(", ")
        : selectedRow.assignApp || [];

      setEditEntry({
        ...selectedRow,
        assignApp: assignAppArray
      });
    }
  }, [selectedRow]);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => setShow(true), 10);
      fetchKioskApps();
      fetchDeviceGroups();
    } else {
      setShow(false);
    }
  }, [isOpen]);

  const fetchKioskApps = async () => {
    try {
      const response = await getKioskSettings();
      const data = response?.data?.data || [];
      if (Array.isArray(data) && data.length > 0) {
        setKioskApps(data);
      } else {
        // Use dummy data if API returns empty or fails
        setKioskApps([
          { kiosk_setting_id: 1, name: "NDA" },
          { kiosk_setting_id: 2, name: "re-check-in" },
          { kiosk_setting_id: 3, name: "Doctor's Office Visit" },
          { kiosk_setting_id: 4, name: "Expedite Check-in" },
          { kiosk_setting_id: 5, name: "Walk-in Guest" },
          { kiosk_setting_id: 6, name: "Guest Verification" },
        ]);
      }
    } catch (error) {
      console.error("Error fetching kiosk settings:", error);
      // Use dummy data on error
      setKioskApps([
        { kiosk_setting_id: 1, name: "NDA" },
        { kiosk_setting_id: 2, name: "re-check-in" },
        { kiosk_setting_id: 3, name: "Doctor's Office Visit" },
        { kiosk_setting_id: 4, name: "Expedite Check-in" },
        { kiosk_setting_id: 5, name: "Walk-in Guest" },
        { kiosk_setting_id: 6, name: "Guest Verification" },
      ]);
    }
  };

  const fetchDeviceGroups = async () => {
    try {
      const response = await getKioskGroups();
      console.log('Device Groups Response:', response);
      if (response?.status && Array.isArray(response?.data?.data)) {
        console.log('Setting device groups from API:', response.data.data);
        setDeviceGroups(response.data.data);
      } else {
        console.log('API response invalid, using dummy data');
        // Use dummy data if API returns empty or fails
        const dummyGroups = [
          { kiosk_group_id: "084c734f-3654-440c-a885-b8244132cf2d", name: "Main Lobby Kiosks" },
          { kiosk_group_id: "184c734f-3654-440c-a885-b8244132cf2e", name: "Emergency Department" },
          { kiosk_group_id: "284c734f-3654-440c-a885-b8244132cf2f", name: "Outpatient Clinic" },
          { kiosk_group_id: "384c734f-3654-440c-a885-b8244132cf30", name: "Pharmacy Kiosks" },
        ];
        console.log('Setting dummy device groups:', dummyGroups);
        setDeviceGroups(dummyGroups);
      }
    } catch (error) {
      console.error("Error fetching device groups:", error);
      // Use dummy data on error
      const dummyGroups = [
        { kiosk_group_id: "084c734f-3654-440c-a885-b8244132cf2d", name: "Main Lobby Kiosks" },
        { kiosk_group_id: "184c734f-3654-440c-a885-b8244132cf2e", name: "Emergency Department" },
        { kiosk_group_id: "284c734f-3654-440c-a885-b8244132cf2f", name: "Outpatient Clinic" },
        { kiosk_group_id: "384c734f-3654-440c-a885-b8244132cf30", name: "Pharmacy Kiosks" },
      ];
      console.log('Error occurred, setting dummy device groups:', dummyGroups);
      setDeviceGroups(dummyGroups);
    }
  };

  if (!isOpen || !editEntry) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  const handleSave = (e) => {
    e.preventDefault();
    // Ensure assignApp is in the right format for the API
    const dataToSave = {
      ...editEntry,
      assignApp: Array.isArray(editEntry.assignApp) ? editEntry.assignApp : [editEntry.assignApp]
    };
    onSave(dataToSave);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Edit Device Group
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form
          onSubmit={handleSave}
          className="bg-white p-6 rounded-lg"
        >
          <div className="flex items-center mb-4">
            <label htmlFor="deviceGroup" className="w-1/4 text-[16px] font-normal">
              Device Group*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={deviceGroups.map(group => ({
                  label: group.name,
                  value: group.kiosk_group_id
                }))}
                value={editEntry.deviceGroup}
                onChange={(value) => {
                  const selectedGroup = deviceGroups.find(group => group.kiosk_group_id === value);
                  setEditEntry({
                    ...editEntry,
                    deviceGroup: selectedGroup ? selectedGroup.name : value,
                    deviceGroupId: value // Store the actual kiosk_group_id
                  });
                }}
                placeholder="Select Device Group"
                className="w-full"
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Assign App*
            </label>
            <div className="w-3/4">
              <select
                className="border p-2 w-full rounded"
                value=""
                onChange={e => {
                  if (e.target.value && !editEntry.assignApp.some(app => (typeof app === 'object' ? app.id : app) === e.target.value)) {
                    const selectedApp = kioskApps.find(app => app.kiosk_setting_id === e.target.value);
                    if (selectedApp) {
                      setEditEntry({
                        ...editEntry,
                        assignApp: [...editEntry.assignApp, { id: selectedApp.kiosk_setting_id, name: selectedApp.name }]
                      });
                    }
                  }
                }}
              >
                <option value="">Select App</option>
                {Array.isArray(kioskApps) && kioskApps.map((app, index) => (
                  <option key={app.kiosk_setting_id || index} value={app.kiosk_setting_id}>
                    {app.name}
                  </option>
                ))}
              </select>
              {editEntry.assignApp && editEntry.assignApp.length > 0 && (
                <div className="mt-2">
                  {editEntry.assignApp.map((app, index) => (
                    <span key={index} className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2 mb-1">
                      {typeof app === 'object' ? app.name : app}
                      <button
                        type="button"
                        className="ml-1 text-red-500"
                        onClick={() => {
                          setEditEntry({ ...editEntry, assignApp: editEntry.assignApp.filter(a => a !== app) });
                        }}
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="status" className="w-1/4 text-[16px] font-normal">
              Status
            </label>
            <div className="w-3/4">
              <select
                id="status"
                name="status"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={editEntry.status || "Active"}
                onChange={e => setEditEntry({ ...editEntry, status: e.target.value })}
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
              </select>
            </div>
          </div>
          <div className="flex gap-4 justify-end">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 bg-gray-400 text-white rounded"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditDeviceGroupModal;
