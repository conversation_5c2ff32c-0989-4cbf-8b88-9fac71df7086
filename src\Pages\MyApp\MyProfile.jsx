import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import userImg from "../../Images/fromimg.svg";
import Identity from '../../Components/MyApp/MyProfile/Identity/Identity';
import Corporate from '../../Components/MyApp/MyProfile/Corporate/Corporate';
import Cards from '../../Components/MyApp/MyProfile/Cards/Cards';
import Access from '../../Components/MyApp/MyProfile/Access/Access';
import Delegates from '../../Components/MyApp/MyProfile/Delegates/Delegates';
import Vehicles from '../../Components/MyApp/MyProfile/Vehicles/Vehicles';
import DetailsCard from '../../Components/Global/DetailsCard';
import EditPhotoModal from '../../Components/Global/ImageAndCamera/EditPhotoModal';
import Requests from '../../Components/MyApp/MyProfile/Requests/Requests';
import {
  getProfile,
  getProfileCards,
  getProfileAccess,
  getProfileVehicles,
  getProfileDelegates,
  getProfileCorporate
} from '../../api/my-profile';

const MyProfile = () => {
  const location = useLocation(); // Get location object
  const queryParams = new URLSearchParams(location.search); // Parse query parameters
  // Normalize tab value to match tab names (capitalize first letter)
  const getNormalizedTab = (tab) => {
    if (!tab) return 'Identity';
    return tab.charAt(0).toUpperCase() + tab.slice(1).toLowerCase();
  };
  const initialTab = getNormalizedTab(queryParams.get('tab'));
  const { t } = useTranslation();

  const [selectedTab, setSelectedTab] = useState(initialTab); // Initialize with query parameter
  const [profileImage, setProfileImage] = useState(null); // Added state for profile image
  const [isModalOpen, setIsModalOpen] = useState(false); // Added state for modal

  // API data states
  const [profileData, setProfileData] = useState(null);
  const [cardsData, setCardsData] = useState(null);
  const [accessData, setAccessData] = useState(null);
  const [vehiclesData, setVehiclesData] = useState(null);
  const [delegatesData, setDelegatesData] = useState(null);
  const [corporateData, setCorporateData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // API fetch functions
  const fetchProfileData = async () => {
    try {
      setLoading(true);
      const data = await getProfile();
      setProfileData(data);
    } catch (err) {
      setError('Failed to fetch profile data');
      console.error('Error fetching profile:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchCardsData = async () => {
    try {
      const data = await getProfileCards();
      setCardsData(data);
    } catch (err) {
      console.error('Error fetching cards:', err);
    }
  };

  const fetchAccessData = async () => {
    try {
      const data = await getProfileAccess();
      setAccessData(data);
    } catch (err) {
      console.error('Error fetching access:', err);
    }
  };

  const fetchVehiclesData = async () => {
    try {
      const data = await getProfileVehicles();
      setVehiclesData(data);
    } catch (err) {
      console.error('Error fetching vehicles:', err);
    }
  };

  const fetchDelegatesData = async () => {
    try {
      const data = await getProfileDelegates();
      setDelegatesData(data);
    } catch (err) {
      console.error('Error fetching delegates:', err);
    }
  };

  const fetchCorporateData = async () => {
    try {
      const data = await getProfileCorporate();
      setCorporateData(data);
    } catch (err) {
      console.error('Error fetching corporate data:', err);
    }
  };

  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc); // Update profile image
    setIsModalOpen(false); // Close modal
  };

  const handleCloseModal = () => {
    setIsModalOpen(false); // Close modal
  };

  // Fetch profile data on component mount
  useEffect(() => {
    fetchProfileData();
  }, []);

  // Fetch data based on selected tab
  useEffect(() => {
    switch (selectedTab) {
      case 'Identity':
        // Profile data is already fetched on mount
        break;
      case 'Corporate':
        fetchCorporateData();
        break;
      case 'Cards':
        fetchCardsData();
        break;
      case 'Access':
        fetchAccessData();
        break;
      case 'Delegates':
        fetchDelegatesData();
        break;
      case 'Vehicles':
        fetchVehiclesData();
        break;
      case 'Requests':
        // Requests might use profile data or have its own API
        break;
      default:
        break;
    }
  }, [selectedTab]);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const tabParam = queryParams.get('tab');
    setSelectedTab(getNormalizedTab(tabParam)); // Update selectedTab when query parameter changes
    const openPhotoModal = queryParams.get('openPhotoModal') === 'true'; // Check if photo modal should open
    if (openPhotoModal) {
      setIsModalOpen(true); // Open photo modal
    }
  }, [location.search]);

  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-20 pt-20">
      {/* Page Heading */}
      <div className="mb-6">
        <h2 className="font-normal text-[24px]  mb-2 text-[#4F2683]">My Profile</h2>
      </div>
      {/* Profile Section */}
      <DetailsCard
        OpenPhotoModal={() => setIsModalOpen(true)} // Open modal on click
        profileImage={profileImage} // Pass profile image
        defaultImage={userImg}
        name={profileData?.name || "ADAM L'THELAN"}
        additionalFields={[
          { label: t('my_profile.type'), value: profileData?.type || t('my_profile.employee') },
          { label: t('my_profile.eid'), value: profileData?.eid || "1222535" },
          { label: t('my_profile.department'), value: profileData?.department || "ABC" },
          { label: t('my_profile.manager'), value: profileData?.manager || "Nema" },
          { label: t('my_profile.status'), value: profileData?.status || t('my_profile.unprinted_badges') },
        ]}
      />

      <div className="flex">
        <div className="w-[12%] mt-8">
          {['Identity', 'Corporate', 'Cards', 'Access', 'Delegates', 'Vehicles', 'Requests'].map((tab) => (
            <button
              key={tab}
              className={`block w-full text-left p-2 mb-2 ${selectedTab === t(tab) ? 'text-[#4F2683] border-l-2 border-[#4F2683]' : 'text-gray-700'}`}
             
              onClick={() => setSelectedTab(tab)}
            >
              {t(`my_profile.tabs.${tab.toLowerCase()}`)}
            </button>
          ))}
        </div>

        <div className="w-[88%] pt-4">
          {selectedTab === 'Identity' && <Identity profileData={profileData} loading={loading} error={error} />}
          {selectedTab === 'Corporate' && <Corporate corporateData={corporateData} loading={loading} />}
          {selectedTab === 'Cards' && <Cards cardsData={cardsData} loading={loading} />}
          {selectedTab === 'Access' && <Access accessData={accessData} loading={loading} />}
          {selectedTab === 'Delegates' && <Delegates delegatesData={delegatesData} loading={loading} />}
          {selectedTab === 'Vehicles' && <Vehicles vehiclesData={vehiclesData} loading={loading} />}
          {selectedTab === 'Requests' && <Requests profileData={profileData} loading={loading} />}
        </div>
      </div>

      {isModalOpen && (
        <EditPhotoModal
          onClose={handleCloseModal} // Close modal on "Close"
          onSave={(imageSrc) => {
            handleImageCaptured(imageSrc); // Save image and close modal
          }}
        />
      )}
    </div>
  );
};

export default MyProfile;
