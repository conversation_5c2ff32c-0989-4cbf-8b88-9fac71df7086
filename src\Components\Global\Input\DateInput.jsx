import React, { useRef, useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./DatePickerStyles.css";
import { FaRegCalendarAlt } from "react-icons/fa";
import CustomDropdown from "../CustomDropdown"; // make sure path is correct

const DateInput = ({ label, value, onChange, placeholder, error, className, allowFutureYears = false }) => {
  const datePickerRef = useRef(null);
  const [inputValue, setInputValue] = useState('');

  const handleClose = () => {
    if (datePickerRef.current) {
      datePickerRef.current.setOpen(false);
    }
  };

  // Parse manual date input in MMM-DD-YYYY format
  const parseManualDate = (input) => {
    const formats = [
      /^([A-Za-z]{3})-(\d{1,2})-(\d{4})$/, // MMM-DD-YYYY
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // MM/DD/YYYY
      /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // YYYY-MM-DD
    ];

    for (const format of formats) {
      const match = input.match(format);
      if (match) {
        let month, day, year;

        if (format === formats[0]) { // MMM-DD-YYYY
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          month = monthNames.indexOf(match[1]);
          day = parseInt(match[2]);
          year = parseInt(match[3]);
        } else if (format === formats[1]) { // MM/DD/YYYY
          month = parseInt(match[1]) - 1;
          day = parseInt(match[2]);
          year = parseInt(match[3]);
        } else { // YYYY-MM-DD
          year = parseInt(match[1]);
          month = parseInt(match[2]) - 1;
          day = parseInt(match[3]);
        }

        if (month >= 0 && month <= 11 && day >= 1 && day <= 31) {
          return new Date(year, month, day);
        }
      }
    }
    return null;
  };

  const handleManualInput = (e) => {
    const input = e.target.value;
    setInputValue(input);

    const parsedDate = parseManualDate(input);
    if (parsedDate && !isNaN(parsedDate.getTime())) {
      onChange(parsedDate);
    }
  };

  // Format date for display
  const formatDateForDisplay = (date) => {
    if (!date) return '';
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';

    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = months[d.getMonth()];
    const day = String(d.getDate()).padStart(2, '0');
    const year = d.getFullYear();

    return `${month}-${day}-${year}`;
  };

  return (
    <div className={className}>
      {label && <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>}
      <div className="relative">
        <DatePicker
          ref={datePickerRef}
          selected={value ? new Date(value) : null}
          onChange={(date) => {
            setInputValue('');
            onChange(date);
          }}
          onChangeRaw={handleManualInput}
          dateFormat="MMM-dd-yyyy"
          placeholderText={placeholder || "MMM-DD-YYYY"}
          className="border h-10 p-2 focus:outline-none rounded-[6px] focus:ring-1 w-full focus:ring-[#4F2683]"
          shouldCloseOnSelect={true}
          onClickOutside={handleClose}
          renderCustomHeader={({ date, changeYear, changeMonth }) => {
            // Generate years based on allowFutureYears prop
            const currentYear = new Date().getFullYear();
            const startYear = allowFutureYears ? currentYear - 50 : currentYear - 100;
            const endYear = allowFutureYears ? currentYear + 50 : currentYear;

            const years = [];
            for (let year = endYear; year >= startYear; year--) {
              years.push({ label: year.toString(), value: year });
            }
        
            const months = [
              "January",
              "February",
              "March",
              "April",
              "May",
              "June",
              "July",
              "August",
              "September",
              "October",
              "November",
              "December",
            ].map((label, index) => ({ label, value: index }));
        
            return (
              <div className="flex justify-between items-center gap-2 px-3 py-2 bg-white rounded-t-md border-b">
                <div className="w-1/2">
                  <CustomDropdown
                    options={years}
                    value={date.getFullYear()}
                    onSelect={(val) => changeYear(val)}
                    placeholder="Select Year"
                    borderColor="border-gray-300"
                    rounded="rounded-md"
                  />
                </div>
                <div className="w-1/2">
                  <CustomDropdown
                    options={months}
                    value={date.getMonth()}
                    onSelect={(val) => changeMonth(val)}
                    placeholder="Select Month"
                    borderColor="border-gray-300"
                    rounded="rounded-md"
                  />
                </div>
              </div>
            );
          }}
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <FaRegCalendarAlt className="h-5 w-5 text-gray-400" />
        </div>
      </div>
      {error && <div className="text-red-500 text-sm">{error}</div>}
    </div>
  );
};

export default DateInput;
