import React from "react";
import Button from "../../Global/Button";
import CustomDropdown from "../../Global/CustomDropdown";
import Input from "../../Global/Input/Input";
import DateInput from "../../Global/Input/DateInput";

import { usePatientGuestTypeMasterData } from "../../../hooks/usePatientGuestTypeMasterData";

const AddFriendsForm = ({ onSave, onClose, newEntry, setNewEntry }) => {
  const { relationshipTypes } = usePatientGuestTypeMasterData();

  // Build dropdown options directly from the API data
  const relationshipOptions = relationshipTypes.map((t) => ({
    label: t.value,   // e.g. "Spouse"
    value: t.key,      // e.g. 0
  }));

  // Ensure relationship is set to first option by default if not set
  React.useEffect(() => {
    if (
      relationshipOptions.length > 0 &&
      (newEntry.relationship === undefined || newEntry.relationship === null || newEntry.relationship === "")
    ) {
      setNewEntry({ ...newEntry, relationship: relationshipOptions[0].value });
    }
    // eslint-disable-next-line
  }, [relationshipOptions]);

  // Animation state for side panel
  const [show, setShow] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white p-6 w-[70%] rounded shadow-lg transform transition-transform duration-700 ease-in-out max-w-3xl h-full overflow-y-auto ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex justify-between">
          <h3 className="text-[30px] font-normal text-[#4F2683] mb-2">
            Add Friends & Family
          </h3>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mb-4" />
        <form
          className="bg-white p-2 rounded-lg"
          onSubmit={(e) => {
            e.preventDefault();
            onSave();
          }}
        >
          <div className="flex">
            <label className="w-1/4 flex items-center h-11 text-[16px] font-normal text-[#333333]">
              First Name *
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full mb-4"
                placeholder="First Name"
                value={newEntry.name}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, name: e.target.value })
                }
                required
              />
            </div>
          </div>

          <div className="flex">
            <label className="w-1/4 flex items-center h-11 text-[16px] font-normal text-[#333333]">
              Last Name *
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full mb-4"
                placeholder="Last Name"
                value={newEntry.lastName}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, lastName: e.target.value })
                }
                required
              />
            </div>
          </div>

          <div className="flex">
            <label className="w-1/4 flex items-center h-11 text-[16px] font-normal text-[#333333]">
              Relationship
            </label>
            <div className="w-3/4 mb-4">
              <CustomDropdown
                options={relationshipOptions}
                value={
                  newEntry.relationship !== undefined && newEntry.relationship !== null && newEntry.relationship !== ""
                    ? Number(newEntry.relationship)
                    : null
                }
                onSelect={(selectedValue) => {
                  setNewEntry({ ...newEntry, relationship: Number(selectedValue) });
                }}
                bgColor="bg-[white] text-black"
                textColor="text-black"
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
                rounded="rounded-[10px]"
                className="p-2 border h-11 rounded-[10px] focus:outline-none focus:ring-1"
              />
            </div>
          </div>

          <div className="flex">
            <label className="w-1/4 flex items-center h-11 text-[16px] font-normal text-[#333333]">
              Phone
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full mb-4"
                placeholder="Phone"
                value={newEntry.phone}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, phone: e.target.value })
                }
                type="tel"
              />
            </div>
          </div>

          <div className="flex">
            <label className="w-1/4 flex items-center h-11 text-[16px] font-normal text-[#333333]">
              Email
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full mb-4"
                placeholder="Email"
                value={newEntry.email}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, email: e.target.value })
                }
                type="email"
              />
            </div>
          </div>
<div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Date Of Birth
            </label>
            <div className="w-3/4">
              <DateInput
                className="w-full rounded-sm"
                placeholder="YYYY-MM-DD"
                value={newEntry.dateDenied}
                onChange={(date) => setNewEntry({ ...newEntry, dateDenied: date })}
              />
            </div>
          </div>
          <div className="flex justify-center gap-4">
            <Button type="cancel" onClick={onClose} label="Cancel" />
            <Button type="primary" label="Save" />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddFriendsForm;
