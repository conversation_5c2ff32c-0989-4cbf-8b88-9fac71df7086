import React, { useState, useRef, useCallback } from "react";
import SearchBar from "../Global/SearchBar";
import Button from "../Global/Button";
import { searchVisits } from "../../api/visitor-hub";


// Debounce utility
function debounce(fn, delay) {
  let timer = null;
  return (...args) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn(...args), delay);
  };
}

const CreateVisitorModal = ({ isOpen, onClose, onSubmit, placeholder, onHostSelect }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [selectedHost, setSelectedHost] = useState(null);

  const containerRef = useRef(null);

  // Debounced host input handler
  const debouncedHostInputChange = useCallback(
    debounce((value) => {
      if (value.trim()) {
        searchVisits({ search: value, facility_id: localStorage.getItem("selectedFacility"), type: 1 })
          .then((res) => {
            setSearchResults(res?.data?.data || []);
            setIsDropdownVisible(true);
          })
          .catch(() => {
            setSearchResults([]);
            setIsDropdownVisible(false);
          });
      } else {
        setSearchResults([]);
        setIsDropdownVisible(false);
      }
    }, 300),
    []
  );

  const handleInputChange = (value) => {
    setSearchTerm(value);
    setSelectedHost(null);
    debouncedHostInputChange(value);
  };

  const handleResultClick = (host) => {
    // If onHostSelect is provided, use it (behave like HostSearch)
    if (onHostSelect) {
      onHostSelect(host);
      onClose();
      return;
    }

    // Otherwise, use the original behavior for creating visitor
    setSelectedHost(host);
    setSearchTerm(host.name);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedHost) {
      alert("Please select a host.");
      return;
    }

    // Final submit with selected host
    onSubmit({
      hostName: selectedHost.name,
      hostId: selectedHost.id,
      hostEid: selectedHost.eid,
    });

    // Reset state
    setSearchTerm("");
    setSelectedHost(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
      <div className="bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-[24px] text-[#4F2683] font-semibold">
            {onHostSelect ? "Select Host" : "Create Visit"}
          </h2>
          <button
            type="button"
            className="flex items-center justify-center bg-[#4F2683] text-white text-xl rounded-full h-8 w-8 hover:bg-[#6A3BAA]"
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
          >
            ×
          </button>
        </div>

        {/* Form */}
        {onHostSelect ? (
          // When used as host selector, just show the search without form
          <div className="w-full relative" ref={containerRef}>
            <SearchBar
              placeholder={placeholder || "Search By Host Name, EID"}
              value={searchTerm}
              onInputChange={handleInputChange}
              borderColor="#4F2683"
            />

            {isDropdownVisible && (
              <div
                className="w-full mt-2 absolute bg-white border rounded-md shadow-lg z-50 overflow-y-auto"
                style={{ maxHeight: "200px" }}
              >
                {searchResults.length > 0 ? (
                  searchResults.map((host) => (
                    <div
                      key={host.id}
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b"
                      onClick={() => handleResultClick(host)}
                    >
                      <div className="font-medium">{host.name}</div>
                      <div className="text-sm text-gray-500">EID-{host.eid}</div>
                    </div>
                  ))
                ) : (
                  <div className="px-3 py-2 text-[#4F2683]">No results found</div>
                )}
              </div>
            )}
          </div>
        ) : (
          // When used for creating visitor, show the form
          <form onSubmit={handleSubmit}>
            <div className="w-full relative" ref={containerRef}>
              <SearchBar
                placeholder={placeholder || "Search By Host Name, EID"}
                value={searchTerm}
                onInputChange={handleInputChange}
                borderColor="#4F2683"
              />

              {isDropdownVisible && (
                <div
                  className="w-full mt-2 absolute bg-white border rounded-md shadow-lg z-50 overflow-y-auto"
                  style={{ maxHeight: "200px" }}
                >
                  {searchResults.length > 0 ? (
                    searchResults.map((host) => (
                      <div
                        key={host.id}
                        className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b"
                        onClick={() => handleResultClick(host)}
                      >
                        <div className="font-medium">{host.name}</div>
                        <div className="text-sm text-gray-500">EID-{host.eid}</div>
                      </div>
                    ))
                  ) : (
                    <div className="px-3 py-2 text-[#4F2683]">No results found</div>
                  )}
                </div>
              )}
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default CreateVisitorModal;
