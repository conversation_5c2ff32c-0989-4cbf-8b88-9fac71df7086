import { useState, useEffect } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";
import { STATUSES } from "../api/static";

let statusMasterDataCache = null;

export const useStatusMasterData = () => {
  const [statuses, setStatuses] = useState([]);

  useEffect(() => {
    const fetchStatuses = async () => {
      if (statusMasterDataCache) {
        setStatuses(statusMasterDataCache);
        return;
      }
      try {
        const res = await getMasterData({ groups: "patient_status" });
        if (res.data?.patient_status && Array.isArray(res.data.patient_status)) {
          statusMasterDataCache = res.data.patient_status;
          setStatuses(res.data.patient_status);
        } else {
          throw new Error("API not available");
        }
      } catch (error) {
        // Fallback to static data
        const fallbackStatuses = STATUSES.map((status, index) => ({
          id: index + 1,
          name: status
        }));
        statusMasterDataCache = fallbackStatuses;
        setStatuses(fallbackStatuses);
      }
    };

    fetchStatuses();
  }, []);

  return { statuses };
};