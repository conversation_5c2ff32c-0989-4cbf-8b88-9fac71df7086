import React, { forwardRef, useState } from 'react';
import { Rect, Text, Group, Image } from 'react-konva';
import { useDispatch } from 'react-redux';
import { updateElement } from '../../../redux/idDesignerSlice';
import imageManager from '../../../utils/imageManager';

const ImageElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const dispatch = useDispatch();
  const [image, setImage] = useState(null);
  const [isDragOver] = useState(false);

  // Check for image source - could be from element.src (URL) or image manager (file)
  const imageUrl = element.src || imageManager.getImageUrl(element.id);
  const hasImage = imageUrl && imageUrl.trim() !== '';

  // Load image when src changes
  React.useEffect(() => {
    if (imageUrl) {
      const img = new window.Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        setImage(img);

        // Auto-adjust element dimensions to maintain aspect ratio if enabled
        if (element.maintainAspectRatio && img.naturalWidth && img.naturalHeight) {
          const aspectRatio = img.naturalWidth / img.naturalHeight;
          const currentAspectRatio = element.width / element.height;

          if (Math.abs(aspectRatio - currentAspectRatio) > 0.01) {
            // Adjust height to maintain aspect ratio based on current width
            const newHeight = element.width / aspectRatio;
            dispatch(updateElement({
              id: element.id,
              properties: { height: newHeight }
            }));
          }
        }
      };
      img.onerror = () => {
        console.error('Failed to load image:', imageUrl);
        setImage(null);
      };
      img.src = imageUrl;
    } else {
      setImage(null);
    }
  }, [imageUrl, element.maintainAspectRatio, element.width, element.height, element.id, dispatch]);

  const handleClick = (e) => {
    // Only allow upload for static images (not dynamic)
    if (!hasImage && !isPreviewMode && !element.isDynamic) {
      // Open file dialog
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = element.acceptedTypes || 'image/*';
      input.onchange = (event) => {
        const file = event.target.files[0];
        if (file) {
          try {
            // Add image to image manager and get temporary URL
            const tempUrl = imageManager.addImage(element.id, file, element.imageType || 'static');

            // Update element with temporary URL for immediate display
            // The actual upload will happen when template is saved
            dispatch(updateElement({
              id: element.id,
              properties: {
                src: tempUrl,
                hasFile: true // Flag to indicate this element has a file to upload
              }
            }));
          } catch (error) {
            console.error('Error handling image file:', error);
          }
        }
      };
      input.click();
    } else {
      onClick(e);
    }
  };

  const getPlaceholderText = () => {
    if (element.isDynamic) {
      if (element.imageType === 'photo') {
        return '👤\nPhoto\n(Dynamic)';
      } else if (element.imageType === 'logo') {
        return '🏢\nLogo\n(Dynamic)';
      }
      return '🖼️\nDynamic Image\n(Template)';
    } else {
      if (element.imageType === 'photo') {
        return '👤\nPhoto\nClick to upload';
      } else if (element.imageType === 'logo') {
        return '🏢\nLogo\nClick to upload';
      }
      return '🖼️\nStatic Image\nClick to upload';
    }
  };

  const getPlaceholderColor = () => {
    if (element.isDynamic) {
      // Dynamic images have a different color scheme
      if (element.imageType === 'photo') {
        return 'rgba(79, 38, 131, 0.08)'; // Theme primary with transparency for dynamic photo
      } else if (element.imageType === 'logo') {
        return 'rgba(79, 38, 131, 0.06)'; // Theme primary with transparency for dynamic logo
      }
      return 'rgba(79, 38, 131, 0.04)'; // Very light theme color for dynamic images
    } else {
      // Static images
      if (element.imageType === 'photo') {
        return 'rgba(79, 38, 131, 0.1)'; // Theme primary with transparency for static photo
      } else if (element.imageType === 'logo') {
        return 'rgba(79, 38, 131, 0.12)'; // Theme primary with transparency for static logo
      }
      return '#f5f5f5'; // Gray for static images
    }
  };

  return (
    <Group
      ref={ref}
      x={element.x}
      y={element.y}
      offsetX={0}
      offsetY={0}
      width={element.width}
      height={element.height}
      rotation={element.rotation || 0}
      opacity={element.opacity !== undefined ? element.opacity : 1}
      visible={element.visible !== false}
      draggable={!element.locked && !isPreviewMode}
      onClick={handleClick}
      onDragEnd={onDragEnd}
    >
      {hasImage && image ? (
        <Group
          clipX={0}
          clipY={0}
          clipWidth={element.width}
          clipHeight={element.height}
          clipFunc={(ctx) => {
            const radius = element.borderRadius || 0;
            if (radius > 0) {
              ctx.beginPath();
              ctx.roundRect(0, 0, element.width, element.height, radius);
              ctx.closePath();
            } else {
              ctx.rect(0, 0, element.width, element.height);
            }
          }}
        >
          <Image
            x={0}
            y={0}
            width={element.width}
            height={element.height}
            image={image}
            {...(element.maintainAspectRatio && image && {
              // Calculate dimensions to maintain aspect ratio
              width: (() => {
                const imageAspectRatio = image.naturalWidth / image.naturalHeight;
                const elementAspectRatio = element.width / element.height;

                if (imageAspectRatio > elementAspectRatio) {
                  // Image is wider - fit to width
                  return element.width;
                } else {
                  // Image is taller - fit to height
                  return element.height * imageAspectRatio;
                }
              })(),
              height: (() => {
                const imageAspectRatio = image.naturalWidth / image.naturalHeight;
                const elementAspectRatio = element.width / element.height;

                if (imageAspectRatio > elementAspectRatio) {
                  // Image is wider - fit to width
                  return element.width / imageAspectRatio;
                } else {
                  // Image is taller - fit to height
                  return element.height;
                }
              })(),
              x: (() => {
                const imageAspectRatio = image.naturalWidth / image.naturalHeight;
                const elementAspectRatio = element.width / element.height;

                if (imageAspectRatio > elementAspectRatio) {
                  return 0; // Align to left
                } else {
                  // Center horizontally
                  const imageWidth = element.height * imageAspectRatio;
                  return (element.width - imageWidth) / 2;
                }
              })(),
              y: (() => {
                const imageAspectRatio = image.naturalWidth / image.naturalHeight;
                const elementAspectRatio = element.width / element.height;

                if (imageAspectRatio > elementAspectRatio) {
                  // Center vertically
                  const imageHeight = element.width / imageAspectRatio;
                  return (element.height - imageHeight) / 2;
                } else {
                  return 0; // Align to top
                }
              })(),
            })}
          />
        </Group>
      ) : (
        <>
          {/* Placeholder rectangle */}
          <Rect
            x={0}
            y={0}
            width={element.width}
            height={element.height}
            fill={getPlaceholderColor()}
            stroke={isDragOver ? '#4f2683' : 'rgba(79, 38, 131, 0.3)'}
            strokeWidth={isDragOver ? 2 : 1}
            dash={[5, 5]}
            cornerRadius={element.borderRadius || 0}
          />

          {/* Placeholder text */}
          <Text
            x={0}
            y={0}
            width={element.width}
            height={element.height}
            text={getPlaceholderText()}
            fontSize={Math.min(12, element.width / 8)}
            fill="#666"
            align="center"
            verticalAlign="middle"
          />
        </>
      )}
    </Group>
  );
});

ImageElement.displayName = 'ImageElement';

export default ImageElement;
