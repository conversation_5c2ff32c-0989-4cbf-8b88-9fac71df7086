import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import EditableSection from '../../Components/Global/EditableSection';
import { updateWatchlist } from '../../api/watchList';

const ReasonHandling = ({ data, watchlistId }) => {
  const [reasonData, setReasonData] = useState({
    Reason: '',
    Description: '',
  });

  useEffect(() => {
    if (data) {
      setReasonData({
        Reason: data.reason || '',
        Description: data.description || '',
      });
    }
  }, [data]);

  const handleChange = (key, value) => {
    setReasonData((prev) => ({ ...prev, [key]: value }));
  };

  const handleSave = async (updatedData) => {
    try {
      await updateWatchlist(watchlistId , {
        reason: updatedData.Reason,
        description: updatedData.Description,
      });
      toast.success("Reason & handling details updated successfully");
    } catch (err) {
      toast.error("Failed to update reason & handling details");
      console.error('Error saving reason', err);
    }
  };

  return (
    <EditableSection
      title="Reason Handling"
      data={reasonData}
      onChange={handleChange}
      onSave={handleSave}
      dropdownKeys={['Reason']}
      dropdownOptions={{ Reason: ['Reason 1', 'Reason 2'] }}
      editableKeys={['Reason', 'Description']}
    />
  );
};

export default ReasonHandling;
