import api from "./";

/**
 * Get all device group settings for a kiosk group with search and sorting support.
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @param {object} params - Query parameters for search and sorting.
 * @param {string} [params.search] - Search term for filtering device groups.
 * @param {string} [params.sortBy] - Field to sort by.
 * @param {string} [params.sortOrder] - Sort order (ASC or DESC).
 * @returns {Promise<any>} List of device group settings.
 */
export const getDeviceGroups = async (kioskGroupId, params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  if (params.sortBy) query.sortBy = params.sortBy;
  if (params.sortOrder) query.sortOrder = params.sortOrder;

  const response = await api.get(`/facility/device-groups/${kioskGroupId}`, { params: query });
  return response.data;
};

/**
 * Create device group settings for a kiosk group.
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @param {object[]} settingsData - Array of device group settings to create.
 * @returns {Promise<any>} Created device group settings.
 */
export const createDeviceGroups = async (kioskGroupId, settingsData) => {
  const response = await api.post(`/facility/device-groups/${kioskGroupId}`, settingsData);
  return response.data;
};

/**
 * Get a device group setting by ID for a kiosk group.
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @param {string} deviceGroupId - The ID of the device group setting.
 * @returns {Promise<any>} Device group setting.
 */
export const getDeviceGroupById = async (kioskGroupId, deviceGroupId) => {
  const response = await api.get(`/facility/device-groups/${kioskGroupId}/${deviceGroupId}`);
  return response.data;
};

/**
 * Update a device group setting.
 * @param {string} facilityId - The ID of the facility.
 * @param {string} deviceGroupId - The ID of the device group setting.
 * @param {object} updateData - Data to update.
 * @returns {Promise<any>} Updated device group setting.
 */
export const updateDeviceGroup = async (kioskGroupId, kioskGroupSettingId, updateData) => {
  const response = await api.patch(`/facility/device-groups/${kioskGroupId}/${kioskGroupSettingId}`, updateData);
  return response.data;
};

/**
 * Delete a device group setting by ID.
 * @param {string} facilityId - The ID of the facility.
 * @param {string} deviceGroupId - The ID of the device group setting.
 * @returns {Promise<any>} Deletion confirmation.
 */
export const deleteDeviceGroup = async (facilityId, deviceGroupId) => {
  const response = await api.delete(`/facility/device-groups/${facilityId}/${deviceGroupId}`);
  return response.data;
};

/**
 * Bulk update device group settings for a kiosk group.
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @param {object[]} bulkUpdateData - Array of settings to update.
 * @returns {Promise<any>} Updated settings.
 */
export const bulkUpdateDeviceGroups = async (kioskGroupId, bulkUpdateData) => {
  const response = await api.patch(`/facility/device-groups/${kioskGroupId}/bulk`, bulkUpdateData);
  return response.data;
};
